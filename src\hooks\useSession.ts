'use client';

import { useState, useEffect, useCallback } from 'react';
import { verifySession, getSession, refreshSession } from '@/lib/supabase/session';
import type { User } from '@/utils/auth';

export interface UseSessionReturn {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  sessionExpiry: Date | null;
  timeUntilExpiry: number | null;
  refreshSession: () => Promise<void>;
  isSessionValid: boolean;
}

/**
 * Enhanced session management hook
 * Provides session-specific operations and monitoring
 */
export function useSession(): UseSessionReturn {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);
  const [timeUntilExpiry, setTimeUntilExpiry] = useState<number | null>(null);

  const checkSession = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Verify session and get user
      const sessionUser = await verifySession();
      setUser(sessionUser);

      // Get session details
      const sessionData = await getSession();
      if (sessionData?.expiresAt) {
        const expiry = new Date(sessionData.expiresAt);
        setSessionExpiry(expiry);
        
        // Calculate time until expiry
        const now = new Date();
        const timeLeft = expiry.getTime() - now.getTime();
        setTimeUntilExpiry(timeLeft > 0 ? timeLeft : 0);
      } else {
        setSessionExpiry(null);
        setTimeUntilExpiry(null);
      }
    } catch (err) {
      console.error('Session check error:', err);
      setError(err instanceof Error ? err.message : 'Session check failed');
      setUser(null);
      setSessionExpiry(null);
      setTimeUntilExpiry(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleRefreshSession = useCallback(async () => {
    try {
      setError(null);
      await refreshSession();
      await checkSession(); // Re-check session after refresh
    } catch (err) {
      console.error('Session refresh error:', err);
      setError(err instanceof Error ? err.message : 'Session refresh failed');
    }
  }, [checkSession]);

  // Update time until expiry every minute
  useEffect(() => {
    if (!sessionExpiry) return;

    const interval = setInterval(() => {
      const now = new Date();
      const timeLeft = sessionExpiry.getTime() - now.getTime();
      setTimeUntilExpiry(timeLeft > 0 ? timeLeft : 0);
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [sessionExpiry]);

  // Auto-refresh session when it's close to expiring (within 1 hour)
  useEffect(() => {
    if (!timeUntilExpiry || timeUntilExpiry <= 0) return;

    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
    
    if (timeUntilExpiry < oneHour && timeUntilExpiry > 0) {
      console.log('Session expiring soon, auto-refreshing...');
      handleRefreshSession();
    }
  }, [timeUntilExpiry, handleRefreshSession]);

  // Initial session check
  useEffect(() => {
    checkSession();
  }, [checkSession]);

  const isSessionValid = user !== null && (timeUntilExpiry === null || timeUntilExpiry > 0);

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    sessionExpiry,
    timeUntilExpiry,
    refreshSession: handleRefreshSession,
    isSessionValid
  };
}

/**
 * Hook for monitoring session expiry and providing warnings
 */
export function useSessionMonitor(warningThresholds: number[] = [15 * 60 * 1000, 5 * 60 * 1000]) {
  const { timeUntilExpiry, sessionExpiry, refreshSession } = useSession();
  const [warnings, setWarnings] = useState<{ threshold: number; triggered: boolean }[]>(
    warningThresholds.map(threshold => ({ threshold, triggered: false }))
  );

  useEffect(() => {
    if (!timeUntilExpiry || timeUntilExpiry <= 0) return;

    setWarnings(prev => prev.map(warning => {
      if (!warning.triggered && timeUntilExpiry <= warning.threshold) {
        // Trigger warning
        console.warn(`Session expiring in ${Math.floor(warning.threshold / 60000)} minutes`);
        return { ...warning, triggered: true };
      }
      return warning;
    }));
  }, [timeUntilExpiry]);

  // Reset warnings when session is refreshed
  useEffect(() => {
    if (sessionExpiry) {
      setWarnings(prev => prev.map(warning => ({ ...warning, triggered: false })));
    }
  }, [sessionExpiry]);

  const getNextWarning = () => {
    if (!timeUntilExpiry) return null;
    
    const nextWarning = warnings.find(w => !w.triggered && timeUntilExpiry <= w.threshold);
    return nextWarning ? {
      timeLeft: timeUntilExpiry,
      threshold: nextWarning.threshold,
      message: `Session expires in ${Math.floor(timeUntilExpiry / 60000)} minutes`
    } : null;
  };

  return {
    timeUntilExpiry,
    sessionExpiry,
    refreshSession,
    warnings: warnings.filter(w => w.triggered),
    nextWarning: getNextWarning(),
    isExpiringSoon: timeUntilExpiry !== null && timeUntilExpiry <= warningThresholds[0]
  };
}

/**
 * Hook for automatic session refresh
 */
export function useAutoSessionRefresh(refreshThreshold: number = 24 * 60 * 60 * 1000) {
  const { timeUntilExpiry, refreshSession, isAuthenticated } = useSession();
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);

  useEffect(() => {
    if (!isAuthenticated || !autoRefreshEnabled || !timeUntilExpiry) return;

    if (timeUntilExpiry <= refreshThreshold && timeUntilExpiry > 0) {
      console.log('Auto-refreshing session...');
      refreshSession();
    }
  }, [timeUntilExpiry, refreshThreshold, refreshSession, isAuthenticated, autoRefreshEnabled]);

  return {
    autoRefreshEnabled,
    setAutoRefreshEnabled,
    timeUntilExpiry,
    refreshThreshold
  };
}
