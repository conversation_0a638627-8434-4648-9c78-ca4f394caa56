@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import centralized layout theme management system */
@import './styles/layout.css';

/* =============================================================================
                        CUSTOM ANIMATIONS FOR LOADING COMPONENTS
============================================================================= */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Glassmorphism utilities */
.glass-card {
  @apply bg-white/20 backdrop-blur-xl border border-white/30 shadow-2xl;
}

/* Enhanced border utilities */
.border-3 {
  border-width: 3px;
}

/* Loading component specific styles */
.loading-container {
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #ecfdf5 100%);
}

/* Smooth transitions for loading states */
.loading-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
===============================================================================
                        EDUPRO DESIGN SYSTEM - GLOBALS.CSS
===============================================================================

This file contains the centralized design system for the EduPro application.
All components should use these standardized classes for consistency.

DESIGN SYSTEM OVERVIEW:
- Typography: Consistent font sizes with responsive scaling
- Buttons: Standardized sizes (sm, md, lg) with consistent styling
- Forms: Unified input and select components with proper sizing
- Tables: Consistent header and cell styling with proper spacing
- Cards: Standardized padding and typography hierarchy
- Modals: Consistent sizing and styling across the application

USAGE GUIDELINES:
1. Always use the standardized classes (e.g., btn-primary, form-input-md)
2. Avoid inline styles or custom CSS unless absolutely necessary
3. Use the responsive typography system (text-xs-app, text-sm-app, etc.)
4. Follow the established component hierarchy (card-sm, card-md, card-lg)

COMPONENT SIZES:
- Small (sm): Compact components for dense layouts
- Medium (md): Default size for most components
- Large (lg): Prominent components and headers

===============================================================================
*/

/* Global Typography - Professional Sans Font System */
@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  html {
    scroll-behavior: smooth;
    font-size: 14px; /* Set explicit base font size for rem calculations */
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    line-height: 1.4; /* Tighter line height for compactness */
    color: #1e293b; /* slate-800 */
    background-color: #f8fafc; /* slate-50 */
    font-size: 1rem; /* Use standard 1rem base size for scalability */
  }
}

/* Professional Typography Scale - Rem-based System */
@layer components {
  /* Base typography classes with consistent rem scaling */
  .text-xs-app {
    font-size: 0.75rem; /* 12px - Extra small text */
    line-height: 1rem; /* 16px - Tight line height */
    font-weight: 400;
  }

  .text-sm-app {
    font-size: 0.875rem; /* 14px - Small text */
    line-height: 1.25rem; /* 20px - Comfortable line height */
    font-weight: 400;
  }

  .text-base-app {
    font-size: 1rem; /* 16px - Base text size */
    line-height: 1.5rem; /* 24px - Standard line height */
    font-weight: 400;
  }

  .text-lg-app {
    font-size: 1.125rem; /* 18px - Large text */
    line-height: 1.75rem; /* 28px - Proportional line height */
    font-weight: 500;
  }

  .text-xl-app {
    font-size: 1.25rem; /* 20px - Extra large text */
    line-height: 1.875rem; /* 30px - Proportional line height */
    font-weight: 600;
  }

  .text-2xl-app {
    font-size: 1.5rem; /* 24px - 2X large text */
    line-height: 2rem; /* 32px - Proportional line height */
    font-weight: 700;
  }

  .text-3xl-app {
    font-size: 1.875rem; /* 30px - 3X large text */
    line-height: 2.25rem; /* 36px - Proportional line height */
    font-weight: 700;
  }

  .text-4xl-app {
    font-size: 2.25rem; /* 36px - 4X large text */
    line-height: 2.5rem; /* 40px - Proportional line height */
    font-weight: 800;
  }

  .text-5xl-app {
    font-size: 3rem; /* 48px - 5X large text */
    line-height: 3rem; /* 48px - Tight line height for display */
    font-weight: 800;
  }
  /* Responsive typography classes - Rem-based scaling */
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px - Small on mobile */
    line-height: 1.25rem; /* 20px */
  }

  @media (min-width: 768px) {
    .text-responsive-sm {
      font-size: 1rem; /* 16px - Base on desktop */
      line-height: 1.5rem; /* 24px */
    }
  }

  .text-responsive-base {
    font-size: 1rem; /* 16px - Base on mobile */
    line-height: 1.5rem; /* 24px */
  }

  @media (min-width: 768px) {
    .text-responsive-base {
      font-size: 1.125rem; /* 18px - Larger on desktop */
      line-height: 1.75rem; /* 28px */
    }
  }

  .text-responsive-lg {
    font-size: 1.125rem; /* 18px - Large on mobile */
    line-height: 1.75rem; /* 28px */
  }

  @media (min-width: 768px) {
    .text-responsive-lg {
      font-size: 1.25rem; /* 20px - Extra large on desktop */
      line-height: 1.875rem; /* 30px */
    }
  }

  .text-responsive-xl {
    font-size: 1.25rem; /* 20px - Extra large on mobile */
    line-height: 1.875rem; /* 30px */
  }

  @media (min-width: 768px) {
    .text-responsive-xl {
      font-size: 1.5rem; /* 24px - 2X large on desktop */
      line-height: 2rem; /* 32px */
    }
  }

  .text-responsive-2xl {
    font-size: 1.5rem; /* 24px - 2X large on mobile */
    line-height: 2rem; /* 32px */
  }

  @media (min-width: 768px) {
    .text-responsive-2xl {
      font-size: 1.875rem; /* 30px - 3X large on desktop */
      line-height: 2.25rem; /* 36px */
    }
  }

  .text-responsive-3xl {
    font-size: 1.875rem; /* 30px - 3X large on mobile */
    line-height: 2.25rem; /* 36px */
  }

  @media (min-width: 768px) {
    .text-responsive-3xl {
      font-size: 2.25rem; /* 36px - 4X large on desktop */
      line-height: 2.5rem; /* 40px */
    }
  }

  .text-responsive-4xl {
    font-size: 2.25rem; /* 36px - 4X large on mobile */
    line-height: 2.5rem; /* 40px */
  }

  @media (min-width: 768px) {
    .text-responsive-4xl {
      font-size: 3rem; /* 48px - 5X large on desktop */
      line-height: 3rem; /* 48px */
    }
  }

  .text-responsive-5xl {
    font-size: 2.5rem; /* 40px - 5X large on mobile */
    line-height: 2.75rem; /* 44px */
  }

  @media (min-width: 768px) {
    .text-responsive-5xl {
      font-size: 3.75rem; /* 60px - 6X large on desktop */
      line-height: 3.75rem; /* 60px */
    }
  }
  
  /* Legacy Form Components - Updated to use standardized system */
  .form-input {
    @apply form-input-md;
  }

  .form-input:focus {
    @apply ring-2 ring-indigo-500/50 border-indigo-500/50 shadow-lg shadow-indigo-500/10;
  }

  .form-input.error {
    @apply border-red-300 focus:ring-red-500/50 focus:border-red-500/50 bg-red-50/50;
  }

  .form-input.success {
    @apply border-green-300 focus:ring-green-500/50 focus:border-green-500/50 bg-green-50/50;
  }

  .form-label {
    @apply text-sm-app font-medium text-slate-700 block;
    margin-bottom: 0.375rem; /* Convert mb-1.5 to rem */
  }

  .form-label.required::after {
    content: " *";
    @apply text-red-500;
  }

  .form-error {
    @apply text-xs-app text-red-600 mt-1 flex items-center;
  }

  .form-success {
    @apply text-xs-app text-green-600 mt-1 flex items-center;
  }

  /* Responsive form inputs for mobile */
  @media (max-width: 640px) {
    .form-input {
      @apply form-input-sm;
    }
  }

  .form-button {
    @apply btn-md;
  }

  .form-button-primary {
    @apply btn-primary;
  }

  .form-button-secondary {
    @apply btn-secondary;
  }

  /* Legacy Card components - Updated to use standardized system */
  .card {
    @apply card-md;
  }

  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
  }

  /* Legacy Table components - Updated to use standardized system */
  .table-header {
    @apply table-header-cell;
  }

  .table-cell {
    @apply text-base-app text-slate-700;
  }
  
  /* ===== STANDARDIZED DESIGN SYSTEM ===== */

  /* Button System - Consistent sizing and typography */
  .btn {
    @apply text-base-app font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    font-family: inherit;
    /* Standardized button sizes */
  }

  .btn-sm {
    @apply btn text-sm-app;
    padding: 0.5rem 0.75rem; /* Convert px-3 py-2 to rem */
    height: 2.25rem; /* Keep rem for consistent scaling */
  }

  .btn-md {
    @apply btn text-base-app;
    padding: 0.625rem 1rem; /* Convert px-4 py-2.5 to rem */
    height: 2.5rem; /* Keep rem for consistent scaling */
  }

  .btn-lg {
    @apply btn text-base-app;
    padding: 0.75rem 1.25rem; /* Convert px-5 py-3 to rem */
    height: 2.75rem; /* Keep rem for consistent scaling */
  }

  .btn-primary {
    @apply btn-md bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply btn-md bg-slate-200 text-slate-700 hover:bg-slate-300 focus:ring-slate-500 border border-slate-300;
  }

  .btn-danger {
    @apply btn-md bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply btn-md bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm hover:shadow-md;
  }

  .btn-outline {
    @apply btn-md bg-white text-slate-700 hover:bg-slate-50 focus:ring-slate-500 border border-slate-300 shadow-sm hover:shadow-md;
  }

  /* Form System - Consistent input sizing and typography */
  .form-input-sm {
    @apply text-sm-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.5rem 0.75rem; /* Convert px-3 py-2 to rem */
    height: 2.25rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  .form-input-md {
    @apply text-base-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.625rem 1rem; /* Convert px-4 py-2.5 to rem */
    height: 2.5rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  .form-input-lg {
    @apply text-base-app border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    padding: 0.75rem 1rem; /* Convert px-4 py-3 to rem */
    height: 2.75rem; /* Keep rem for consistent scaling */
    font-family: inherit;
  }

  /* Default form input - medium size */
  .form-input {
    @apply form-input-md;
  }

  /* Select/Dropdown System */
  .form-select-sm {
    @apply form-input-sm appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  .form-select-md {
    @apply form-input-md appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  .form-select-lg {
    @apply form-input-lg appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem; /* Keep rem for consistent scaling */
  }

  /* Default form select - medium size */
  .form-select {
    @apply form-select-md;
  }

  /* Table System - Consistent typography and spacing */
  .table-container {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden;
  }

  .table-header {
    @apply bg-gray-800 text-gray-200;
  }

  .table-header-cell {
    @apply text-left text-sm-app font-medium uppercase tracking-wide;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3rem; /* Keep rem for consistent scaling */
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150 border-b border-gray-200 last:border-b-0;
  }

  .table-cell {
    @apply text-base-app text-gray-900;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3.5rem; /* Keep rem for consistent scaling */
  }

  .table-cell-secondary {
    @apply text-sm-app text-gray-600;
    padding: 0.75rem 1.5rem; /* Convert px-6 py-3 to rem */
    height: 3.5rem; /* Keep rem for consistent scaling */
  }

  /* Card System - Consistent padding and typography */
  .card-sm {
    @apply bg-white rounded-lg shadow-sm border border-slate-200;
    padding: 1rem; /* Convert p-4 to rem */
  }

  .card-md {
    @apply bg-white rounded-lg shadow-sm border border-slate-200;
    padding: 1.5rem; /* Convert p-6 to rem */
  }

  .card-lg {
    @apply bg-white rounded-xl shadow-lg border border-slate-200;
    padding: 2rem; /* Convert p-8 to rem */
  }

  /* Default card - medium size */
  .card {
    @apply card-md;
  }

  .card-title-sm {
    @apply text-base-app font-semibold text-slate-900;
    margin-bottom: 0.5rem; /* Convert mb-2 to rem */
  }

  .card-title-md {
    @apply text-lg-app font-semibold text-slate-900;
    margin-bottom: 0.75rem; /* Convert mb-3 to rem */
  }

  .card-title-lg {
    @apply text-xl-app font-bold text-slate-900;
    margin-bottom: 1rem; /* Convert mb-4 to rem */
  }

  /* Default card title - medium size */
  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700 leading-relaxed;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
    margin-bottom: 0.5rem; /* Convert mb-2 to rem */
  }

  /* Section Header System - Consistent hierarchy */
  .section-header-sm {
    @apply text-lg-app font-semibold text-gray-900 mb-3;
  }

  .section-header-md {
    @apply text-xl-app font-bold text-gray-900 mb-4;
  }

  .section-header-lg {
    @apply text-2xl-app font-bold text-gray-900 mb-6;
  }

  .section-header-xl {
    @apply text-3xl-app font-bold text-gray-900 mb-8;
  }

  /* Page Header System */
  .page-header {
    @apply section-header-lg;
  }

  .page-subtitle {
    @apply text-base-app text-gray-600 mt-1;
  }

  /* Modal System - Consistent sizing and typography */
  .modal-overlay {
    @apply fixed inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90 flex items-center justify-center backdrop-blur-md;
    padding: 1rem; /* Convert p-4 to rem */
    z-index: 9999; /* Ensure modal is above all other elements */
    min-height: 100vh; /* Ensure full viewport coverage */
    min-height: 100dvh; /* Use dynamic viewport height for mobile */
  }

  .modal-container-sm {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-sm mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-md {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-md mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-lg {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-lg mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-xl {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-xl mx-auto relative shadow-2xl border border-white/20;
  }

  /* Default modal - medium size */
  .modal-container {
    @apply modal-container-md;
  }

  .modal-header {
    @apply border-b border-gray-200;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
  }

  .modal-title {
    @apply text-lg-app font-semibold text-gray-900;
  }

  .modal-content {
    @apply text-base-app text-gray-700;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
  }

  .modal-footer {
    @apply border-t border-gray-200 flex items-center justify-end;
    padding: 1rem 1.5rem; /* Convert px-6 py-4 to rem */
    gap: 0.75rem; /* Convert space-x-3 to gap in rem */
  }
  
  /* Modal and overlay components */
  .modal-title {
    @apply text-xl-app font-semibold text-slate-900;
  }
  
  .modal-content {
    @apply text-base-app text-slate-700;
  }
}

/* Custom fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.875rem); /* Convert 30px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-3.125rem); /* Convert -50px to rem */
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(3.125rem); /* Convert 50px to rem */
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modern Auth Modal Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-1.25rem) scale(0.95); /* Convert -20px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown slideIn animation */
@keyframes slideInDropdown {
  from {
    opacity: 0;
    transform: translateY(-0.625rem) scale(0.98); /* Convert -10px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalBackdrop {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(0.75rem); /* Convert 12px to rem */
  }
}

@keyframes shimmer {
  0% {
    background-position: -12.5rem 0; /* Convert -200px to rem */
  }
  100% {
    background-position: calc(12.5rem + 100%) 0; /* Convert 200px to rem */
  }
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(99 102 241 / 0.3);
    box-shadow: 0 0 0 0 rgb(99 102 241 / 0.7);
  }
  70% {
    border-color: rgb(99 102 241 / 0.6);
    box-shadow: 0 0 0 0.1875rem rgb(99 102 241 / 0); /* Convert 3px to rem */
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

/* Modern loading animations for enhanced UI */
.animate-pulseGlow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-modalBackdrop {
  animation: modalBackdrop 0.3s ease-out;
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

/* Loading specific enhancements */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

/* Progress bar enhancements */
.progress-glow {
  box-shadow: 
    0 0 10px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2),
    0 0 30px rgba(59, 130, 246, 0.1);
}

.border-5 {
  border-width: 5px;
}
/* Custom scrollbar */
::-webkit-scrollbar {
  width: 0.5rem; /* Convert 8px to rem */
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 0.25rem; /* Convert 4px to rem */
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 1.25rem 1.25rem; /* Convert 20px to rem */
}

/* Enhanced form field styles */
.form-field-enhanced {
  @apply relative;
}

.form-field-enhanced input:focus + .field-icon {
  @apply text-indigo-500 scale-110;
}

.form-field-enhanced:hover .field-icon {
  @apply text-slate-500;
}

/* Loading spinner enhancement */
@keyframes spin-enhanced {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-enhanced {
  animation: spin-enhanced 1s linear infinite;
}

/* Enhanced sidebar and navigation styles */

/* Sidebar navigation hover effects */
.nav-item-enhanced {
  @apply relative overflow-hidden;
}

.nav-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-item-enhanced:hover::before {
  left: 100%;
}

/* Active nav item glow effect */
.nav-item-active {
  box-shadow:
    0 0 1.25rem rgba(99, 102, 241, 0.3), /* Convert 20px to rem */
    0 0 2.5rem rgba(99, 102, 241, 0.1), /* Convert 40px to rem */
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}  /* Modern Professional Dark Sidebar Theme */
  .sidebar-dark {
    background: linear-gradient(135deg,
      #1e293b 0%,                      /* Slate 800 - Deep dark base */
      #334155 20%,                     /* Slate 700 - Medium dark */
      #374151 40%,                     /* Gray 700 - Rich dark center */
      #334155 60%,                     /* Slate 700 - Medium dark */
      #1e293b 100%                     /* Slate 800 - Deep dark base */
    );
    /* Modern dark morphism effect */
    position: relative;
    box-shadow:
      0.75rem 0 3rem -0.25rem rgba(0, 0, 0, 0.4),
      0 0.5rem 2rem -0.125rem rgba(0, 0, 0, 0.2),
      inset -1px 0 0 rgba(71, 85, 105, 0.8),
      inset 0 1px 0 rgba(71, 85, 105, 0.6),
      inset 0 -1px 0 rgba(15, 23, 42, 0.4);
    backdrop-filter: blur(20px) saturate(1.1);
    /* Professional gradient separator */
    position: relative;
  }/* Beautiful Professional Dark Separator with Accent Gradient */
  .sidebar-dark::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg,
      transparent 0%,
      rgba(34, 197, 94, 0.4) 10%,      /* Green accent */
      rgba(16, 185, 129, 0.5) 25%,     /* Emerald accent */
      rgba(6, 182, 212, 0.6) 40%,      /* Cyan accent */
      rgba(59, 130, 246, 0.7) 50%,     /* Blue accent - center */
      rgba(59, 130, 246, 0.7) 60%,     /* Blue accent - center */
      rgba(6, 182, 212, 0.6) 75%,      /* Cyan accent */
      rgba(16, 185, 129, 0.5) 85%,     /* Emerald accent */
      rgba(34, 197, 94, 0.4) 95%,      /* Green accent */
      transparent 100%
    );
    /* Modern dark glow effects */
    box-shadow: 
      inset 1px 0 0 rgba(71, 85, 105, 0.6),    /* Inner highlight */
      2px 0 8px rgba(59, 130, 246, 0.25),      /* Blue outer glow */
      1px 0 4px rgba(34, 197, 94, 0.15),       /* Green mid glow */
      0 0 12px rgba(6, 182, 212, 0.1);         /* Cyan far glow */
    z-index: 10;
    transition: opacity 0.3s ease;
    animation: separatorGlow 4s ease-in-out infinite;
    border-radius: 0 2px 2px 0;
  }
  /* Enhanced hover effect for the separator */
  .sidebar-dark:hover::after {
    animation-duration: 2s; /* Faster animation on hover */
  }

  /* Dark overlay for modern depth */
  .sidebar-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.1) 0%,       /* Subtle slate highlight */
      rgba(51, 65, 85, 0.05) 50%,      /* Transparent center */
      rgba(15, 23, 42, 0.1) 100%       /* Deep slate shadow */
    );
    pointer-events: none;
    z-index: 1;
  }
  .sidebar-dark .sidebar-header {
    @apply border-slate-600/50;
    background: linear-gradient(135deg,
      #1e293b 0%,                      /* Slate 800 - Deep dark */
      #334155 30%,                     /* Slate 700 - Medium dark */
      #374151 50%,                     /* Gray 700 - Rich center */
      #334155 70%,                     /* Slate 700 - Medium dark */
      #1e293b 100%                     /* Slate 800 - Deep dark */
    );
    backdrop-filter: blur(24px) saturate(1.1);
    box-shadow:
      0 3px 12px rgba(0, 0, 0, 0.3),
      0 1px 6px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(71, 85, 105, 0.6),
      inset 0 -1px 0 rgba(15, 23, 42, 0.4);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-brand {
    @apply font-bold;
    background: linear-gradient(135deg, #10b981 0%, #34d399 40%, #6ee7b7 80%, #a7f3d0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
  }  .sidebar-dark .sidebar-toggle-btn {
    @apply text-slate-300 hover:text-white transition-all duration-300;
    @apply hover:bg-gradient-to-r hover:from-slate-700 hover:to-slate-600;
    @apply hover:shadow-lg hover:shadow-slate-900/30;
    border-radius: 0.5rem;
  }  .sidebar-dark .sidebar-nav-item {
    @apply text-slate-200 transition-all duration-300 relative overflow-hidden;
    @apply hover:text-white;
    margin: 0;
    border-radius: 0.75rem;
    backdrop-filter: blur(16px) saturate(1.1);
    border: 1px solid rgba(71, 85, 105, 0.3);
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.2) 0%,       /* Slate glass */
      rgba(51, 65, 85, 0.1) 50%,       /* Transparent center */
      rgba(71, 85, 105, 0.2) 100%      /* Slate glass */
    );
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(71, 85, 105, 0.4),
      inset 0 -1px 0 rgba(15, 23, 42, 0.2);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-nav-item:hover {
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.4) 0%,
      rgba(51, 65, 85, 0.3) 50%,
      rgba(71, 85, 105, 0.4) 100%
    );
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.3),
      0 3px 10px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(71, 85, 105, 0.6),
      inset 0 -1px 0 rgba(15, 23, 42, 0.3);
    transform: translateY(-2px);
  }  .sidebar-dark .sidebar-nav-item::before {
    content: '';
    @apply absolute left-0 top-0 bottom-0 w-1 transition-all duration-300;
    background: linear-gradient(180deg, transparent 0%, #34d399 50%, transparent 100%);
    opacity: 0;
    transform: scaleY(0);
    border-radius: 0 0.25rem 0.25rem 0;
  }

  .sidebar-dark .sidebar-nav-item:hover::before {
    opacity: 1;
    transform: scaleY(1);
  }

  /* Modern Active item styling with sophisticated accent */
  .sidebar-dark .sidebar-nav-item.active {
    @apply text-white relative;
    background: linear-gradient(135deg,
      rgba(16, 185, 129, 0.25) 0%,     /* Emerald accent */
      rgba(34, 197, 94, 0.3) 25%,      /* Green accent */
      rgba(59, 130, 246, 0.25) 50%,    /* Blue accent center */
      rgba(34, 197, 94, 0.3) 75%,      /* Green accent */
      rgba(16, 185, 129, 0.25) 100%    /* Emerald accent */
    );
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.4),
      0 4px 12px rgba(16, 185, 129, 0.2),
      inset 0 1px 0 rgba(71, 85, 105, 0.8),
      inset 0 -1px 0 rgba(15, 23, 42, 0.4);
    border-left: 3px solid rgba(16, 185, 129, 0.9);
    border-color: rgba(16, 185, 129, 0.6);
    backdrop-filter: blur(20px) saturate(1.2);
    position: relative;
    z-index: 3;
  }

  /* Active item dark glow effect */
  .sidebar-dark .sidebar-nav-item.active::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
      rgba(16, 185, 129, 0.15) 0%,
      rgba(34, 197, 94, 0.12) 50%,
      rgba(16, 185, 129, 0.15) 100%
    );
    border-radius: 0.875rem;
    z-index: -1;
    filter: blur(4px);
  }
  .sidebar-dark .sidebar-nav-item .sidebar-label {
    @apply text-slate-200 font-medium text-sm;
  }

  .sidebar-dark .sidebar-nav-item.active .sidebar-label {
    @apply text-white font-semibold text-sm;
    text-shadow: 0 1px 2px rgba(16, 185, 129, 0.3);
  }

  .sidebar-dark .sidebar-subitem {
    @apply text-slate-400 transition-all duration-300 relative;
    @apply hover:text-emerald-300;
    margin: 0.25rem 0 0.25rem 1.5rem; /* Adjusted left margin for connecting lines */
    padding: 0.5rem 0.75rem; /* Simplified padding since icons handle spacing */
    border-radius: 0.5rem;
    background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.4) 0%,
      rgba(15, 23, 42, 0.5) 100%
    );
    border: 1px solid rgba(71, 85, 105, 0.25);
    box-shadow:
      inset 0 1px 0 rgba(71, 85, 105, 0.2),
      0 1px 2px rgba(0, 0, 0, 0.15);
    font-size: 0.75rem; /* Smaller font size for hierarchy */
    font-weight: 400; /* Lighter weight for sub-items */
    position: relative;
    max-width: calc(100% - 1.5rem); /* Ensure proper containment */
    backdrop-filter: blur(8px) saturate(1.05);
    z-index: 1; /* Ensure sub-items are above connecting lines */
  }

  .sidebar-dark .sidebar-subitem.active {
    @apply text-emerald-200 bg-gradient-to-r;
    background: linear-gradient(135deg,
      rgba(16, 185, 129, 0.2) 0%,
      rgba(34, 197, 94, 0.15) 50%,
      rgba(6, 182, 212, 0.1) 100%
    );
    border: 1px solid rgba(16, 185, 129, 0.4);
    box-shadow:
      0 2px 8px rgba(16, 185, 129, 0.15),
      inset 0 1px 0 rgba(16, 185, 129, 0.2),
      0 0 0 1px rgba(16, 185, 129, 0.1);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(16, 185, 129, 0.3);
  }

  .sidebar-dark .sidebar-subitem:hover {
    @apply text-emerald-200;
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.4) 0%,
      rgba(51, 65, 85, 0.45) 100%
    );
    border-color: rgba(16, 185, 129, 0.5);
    box-shadow: 
      0 3px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(71, 85, 105, 0.4),
      0 0 0 1px rgba(16, 185, 129, 0.2);
    transform: translateX(2px);
  }

  /* Sub-item icons styling */
  .sidebar-dark .sidebar-subitem-icon {
    @apply text-slate-500 transition-colors duration-300;
    opacity: 0.7;
  }

  .sidebar-dark .sidebar-subitem:hover .sidebar-subitem-icon {
    @apply text-emerald-400;
    opacity: 1;
  }

  .sidebar-dark .sidebar-subitem.active .sidebar-subitem-icon {
    @apply text-emerald-300;
    opacity: 1;
    filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
  }

  .sidebar-dark .sidebar-profile {
    @apply text-slate-200 transition-all duration-300;
    @apply hover:text-white;
    margin: 0 0.5rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(18px) saturate(1.1);
    border: 1px solid rgba(71, 85, 105, 0.4);
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.3) 0%,
      rgba(51, 65, 85, 0.2) 30%,
      rgba(30, 41, 59, 0.15) 50%,
      rgba(51, 65, 85, 0.2) 70%,
      rgba(71, 85, 105, 0.3) 100%
    );
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(71, 85, 105, 0.5),
      inset 0 -1px 0 rgba(15, 23, 42, 0.3);
    position: relative;
    z-index: 2;
  }

  .sidebar-dark .sidebar-profile:hover {
    background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.6) 0%,
      rgba(51, 65, 85, 0.4) 50%,
      rgba(71, 85, 105, 0.6) 100%
    );
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.4),
      0 4px 12px rgba(16, 185, 129, 0.15),
      inset 0 1px 0 rgba(71, 85, 105, 0.7),
      inset 0 -1px 0 rgba(15, 23, 42, 0.4);
    border-color: rgba(16, 185, 129, 0.5);
    transform: translateY(-1px);
  }
  .sidebar-dark .sidebar-tooltip {
    @apply absolute left-full ml-4 px-3 py-2 text-white text-sm rounded-lg shadow-xl;
    @apply opacity-0 invisible transform -translate-y-1/2 transition-all duration-300 ease-out;
    background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.95) 0%,       /* Dark slate */
      rgba(51, 65, 85, 0.9) 50%,       /* Medium slate */
      rgba(71, 85, 105, 0.95) 100%     /* Light slate */
    );
    box-shadow:
      0 12px 30px rgba(0, 0, 0, 0.4),
      0 6px 16px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(71, 85, 105, 0.6);
    backdrop-filter: blur(24px) saturate(1.1);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    top: 50%;
    z-index: 9999;
    pointer-events: none;
  }

  .sidebar-dark .sidebar-tooltip::before {
    content: '';
    @apply absolute right-full top-1/2 -translate-y-1/2;
    border: 6px solid transparent;
    border-right: 6px solid rgba(51, 65, 85, 0.98);
    filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.2));
  }

  .sidebar-dark .sidebar-tooltip.show {
    @apply opacity-100 visible;
    transform: translateY(-50%) translateX(4px);
    animation: tooltipSlideIn 0.3s ease-out forwards;
  }

  /* Enhanced icon colors for dark theme */
  .sidebar-dark .sidebar-nav-item svg {
    @apply text-slate-300 transition-colors duration-300;
  }

  .sidebar-dark .sidebar-nav-item:hover svg {
    @apply text-white;
  }

  .sidebar-dark .sidebar-nav-item.active svg {
    @apply text-emerald-200;
    filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
  }

  /* Sub-navigation container styling */
  .sidebar-dark .sidebar-nav-item + div {
    position: relative;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .sidebar-dark .sidebar-nav-item + div::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(180deg, 
      transparent 0%, 
      rgba(71, 85, 105, 0.3) 20%, 
      rgba(71, 85, 105, 0.3) 80%, 
      transparent 100%
    );
  }

  /* Icon styling for collapsed sidebar */
  .sidebar-item-collapsed svg {
    @apply text-slate-300 transition-colors duration-300;
  }

  .sidebar-item-collapsed:hover svg {
    @apply text-white;
  }

  .sidebar-item-collapsed.active svg {
    @apply text-emerald-200;
    filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
  }
  .sidebar-dark .sidebar-icon-collapsed,
  .sidebar-dark .sidebar-icon-expanded {
    @apply text-slate-300 transition-colors duration-300;
    width: 1.25rem;
    height: 1.25rem;
  }

  .sidebar-dark .sidebar-nav-item:hover .sidebar-icon-collapsed,
  .sidebar-dark .sidebar-nav-item:hover .sidebar-icon-expanded {
    @apply text-white;
  }

  .sidebar-dark .sidebar-nav-item.active .sidebar-icon-collapsed,
  .sidebar-dark .sidebar-nav-item.active .sidebar-icon-expanded {
    @apply text-emerald-300;
    filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
  }

  /* Smooth animations for dark theme */
  .sidebar-dark .sidebar-nav-item,
  .sidebar-dark .sidebar-subitem,
  .sidebar-dark .sidebar-profile {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-dark .sidebar-nav-item:hover,
  .sidebar-dark .sidebar-subitem:hover,
  .sidebar-dark .sidebar-profile:hover {
    animation: darkHoverPulse 0.6s ease-in-out;
  }  /* Modern Dark Collapse Toggle Arrow */
  .sidebar-collapse-toggle {
    @apply absolute top-1/2;
    @apply bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full;
    @apply flex items-center justify-center cursor-pointer transition-all duration-300;
    @apply hover:from-emerald-500 hover:to-teal-500 hover:scale-110;
    @apply shadow-lg shadow-emerald-600/40;
    right: -1.25rem;
    transform: translateY(-50%);
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid rgba(71, 85, 105, 0.8);
    z-index: 9999 !important;
  }

  .sidebar-collapse-toggle:hover {
    box-shadow: 0 0 1.5625rem rgba(16, 185, 129, 0.6);
  }

  .sidebar-collapse-toggle svg {
    @apply w-5 h-5 text-white transition-transform duration-300; /* Increased icon size */
  }

  .sidebar-collapse-toggle svg {
    @apply w-4 h-4 text-white transition-transform duration-300;
  }

  .sidebar-collapse-toggle.collapsed svg {
    @apply rotate-180;
  }  /* Professional Dark Header to Match Dark Sidebar */
  .header-dark {
    @apply border-slate-600/50;
    background: linear-gradient(135deg,
      #1e293b 0%,                      /* Slate 800 - Deep dark to match sidebar */
      #334155 30%,                     /* Slate 700 - Medium dark */
      #374151 50%,                     /* Gray 700 - Rich center */
      #334155 70%,                     /* Slate 700 - Medium dark */
      #1e293b 100%                     /* Slate 800 - Deep dark to match sidebar */
    );
    backdrop-filter: blur(24px) saturate(1.1);
    box-shadow:
      0 3px 12px rgba(0, 0, 0, 0.3),
      0 1px 6px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(71, 85, 105, 0.6),
      inset 0 -1px 0 rgba(15, 23, 42, 0.4);
  }
  
  .header-dark .header-title {
    @apply text-white;
    font-size: 1.2375rem;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .header-dark .header-breadcrumb {
    @apply text-slate-300;
  }

  .header-dark .header-breadcrumb-current {
    @apply text-slate-100;
  }

  .header-dark .header-search-input {
    @apply bg-slate-700 border-slate-600 text-white placeholder-slate-400;
    @apply focus:bg-slate-600 focus:border-slate-500 focus:ring-2 focus:ring-emerald-500/30;
  }

  .header-dark .header-action-btn {
    @apply text-slate-300 hover:text-white hover:bg-slate-700/50;
    transition: all 0.3s ease;
  }

  .header-dark .header-profile-dropdown {
    @apply bg-slate-800 border-slate-600 shadow-black/40;
    backdrop-filter: blur(20px);
  }

  .header-dark .header-profile-name {
    @apply text-white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .header-dark .header-profile-role {
    @apply text-slate-300;
  }

  .header-dark .header-dropdown-item {
    @apply text-slate-200 hover:bg-slate-700 hover:text-white;
    transition: all 0.2s ease;
  }

  .header-dark .header-dropdown-item.danger {
    @apply text-red-400 hover:bg-red-900/50 hover:text-red-300;
  }

  /* Consistent header height */
  .header-height {
    @apply h-14; /* 56px height for compact header */
  }

  .sidebar-header-height {
    @apply h-14; /* 56px height to match header */
  }

  /* Enhanced glass morphism effect */
  .glass-morphism-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Professional shadow utilities */
  .shadow-professional {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .shadow-professional-lg {
    box-shadow: 
      0 10px 25px rgba(0, 0, 0, 0.1),
      0 6px 10px rgba(0, 0, 0, 0.12);
  }
  /* Enhanced animation keyframes */
  @keyframes tooltipSlideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(4px);
    }
  }

  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
  }

  /* System status indicator animation */
  @keyframes pulse-gentle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  .pulse-gentle {
    animation: pulse-gentle 2s infinite ease-in-out;
  }

  /* Enhanced gradient text */
  .gradient-text-professional {
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #334155 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Custom sidebar font sizes - 5% smaller */
  .text-xs-small {
    font-size: 0.7125rem; /* 0.75rem * 0.95 = 0.7125rem */
    line-height: 1.1875rem; /* 1.25rem * 0.95 = 1.1875rem */
  }

  .text-sm-small {
    font-size: 0.83125rem; /* 0.875rem * 0.95 = 0.83125rem */
    line-height: 1.30625rem; /* 1.375rem * 0.95 = 1.30625rem */
  }

  /* Custom sidebar font sizes - 5% larger than original */
  .text-xs-large {
    font-size: 0.7875rem; /* 0.75rem * 1.05 = 0.7875rem */
    line-height: 1.3125rem; /* 1.25rem * 1.05 = 1.3125rem */
  }

  .text-sm-large {
    font-size: 0.91875rem; /* 0.875rem * 1.05 = 0.91875rem */
    line-height: 1.44375rem; /* 1.375rem * 1.05 = 1.44375rem */
  }

  /* Custom sidebar widths - 3% wider */
  .w-14-plus {
    width: 3.625rem; /* 56px * 1.03 = 57.68px ≈ 58px = 3.625rem */
  }

  .w-62 {
    width: 15.5rem; /* 240px * 1.03 = 247.2px ≈ 248px = 15.5rem */
  }

  /* Custom margins to match sidebar widths */
  .ml-14-plus {
    margin-left: 3.625rem; /* Match w-14-plus */
  }

  .ml-62 {
    margin-left: 15.5rem; /* Match w-62 */
  }

  /* Custom left positioning for separator */
  .left-14-plus {
    left: 3.625rem; /* Match w-14-plus */
  }

  .left-62 {
    left: 15.5rem; /* Match w-62 */
  }

  /* Smooth Animation for Tooltips */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-10px) translateY(-50%);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateY(-50%);
    }
  }

  /* Collapsed Sidebar Styles */
  @layer components {    /* Enhanced icons for collapsed sidebar with perfect centering */
    .sidebar-icon-collapsed {
      @apply w-6 h-6; /* Increased size for better visibility while staying within bounds */
      flex-shrink: 0; /* Prevent icon from shrinking */
      display: block;
      margin: 0 auto; /* Ensure perfect horizontal centering */
    }

    .sidebar-icon-expanded {
      @apply w-5 h-5; /* Keep current size for expanded state */
      flex-shrink: 0; /* Prevent icon from shrinking */
    }    /* Enhanced hover states for collapsed sidebar with professional light theme */
    .sidebar-item-collapsed {
      @apply relative flex items-center justify-center rounded-xl transition-all duration-300;
      @apply text-slate-600 hover:text-slate-800;
      /* Perfect centering properties */
      width: 3rem;
      height: 3rem;
      margin: 0 auto; /* Explicit centering */
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(148, 163, 184, 0.3);
      backdrop-filter: blur(8px);
    }
    
    .sidebar-item-collapsed:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.12) 100%);
      border-color: rgba(59, 130, 246, 0.4);
      transform: scale(1.05);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    }

    /* Professional collapsed sidebar active icon styling */
    .sidebar-item-collapsed.active {
      @apply text-slate-800;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(147, 197, 253, 0.15) 100%);
      border-color: rgba(59, 130, 246, 0.5);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.25);
    }

    .sidebar-item-collapsed:hover .sidebar-tooltip {
      @apply opacity-100 visible;
      transform: translateY(-50%) translateX(4px);
      animation: tooltipSlideIn 0.3s ease-out forwards;
    }    /* Additional collapsed sidebar improvements */
  .sidebar-dark nav {
    overflow: visible; /* Allow tooltips to show outside sidebar bounds */
  }/* Perfect centering for collapsed navigation items */
  .sidebar-dark.w-16 nav {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0; /* Remove all padding for perfect centering */
  }

  /* Center all collapsed items perfectly within the 64px sidebar */
  .sidebar-dark .sidebar-item-collapsed {
    /* Perfect centering with explicit dimensions */
    width: 3rem; /* 48px */
    height: 3rem; /* 48px */
    margin: 0 auto; /* Center horizontally */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Prevent profile section overflow in collapsed mode */
  .sidebar-dark .sidebar-profile.sidebar-item-collapsed {
    position: relative;
    left: 0;
    transform: none;
  }

    /* Enhanced sidebar typography */
    .sidebar-label {
      @apply text-base-app font-medium; /* Uses the increased font size */
    }

    .sidebar-sublabel {
      @apply text-sm-app; /* Uses the increased font size */
    }

    .sidebar-brand {
      @apply text-lg-app font-bold; /* Uses the increased font size */
    }

    .sidebar-profile-name {
      @apply text-sm-app font-medium; /* Uses the increased font size */
    }

    .sidebar-profile-role {
      @apply text-xs-app; /* Uses the increased font size */
    }
  }  /* Removed glassmorphism effects since sidebar now matches header solid background */

  /* Fixed Issue #1: Removed transform rules that cause active item shifting */
  /* .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed):hover {
    transform: translateX(2px);
  }

  .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed).active {
    transform: translateX(4px);
  } *//* Subtle glow effects */
  .sidebar-dark .sidebar-brand {
    filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.4));
  }

  /* Enhanced profile section */
  .sidebar-dark .sidebar-profile {
    @apply mx-2 rounded-xl;
    backdrop-filter: blur(10px);
  }

  @keyframes separatorGlow {
  0%, 100% {
    opacity: 0.8;
    box-shadow: 
      2px 0 8px rgba(59, 130, 246, 0.25),
      1px 0 4px rgba(34, 197, 94, 0.15),
      0 0 12px rgba(6, 182, 212, 0.1);
  }
  50% {
    opacity: 1;
    box-shadow: 
      3px 0 12px rgba(59, 130, 246, 0.35),
      2px 0 6px rgba(34, 197, 94, 0.25),
      0 0 16px rgba(6, 182, 212, 0.18);
  }
}

/* Dark theme specific animations */
@keyframes darkHoverPulse {
  0% { 
    transform: translateY(0) scale(1); 
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  50% { 
    transform: translateY(-1px) scale(1.02); 
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(16, 185, 129, 0.15);
  }
  100% { 
    transform: translateY(-2px) scale(1); 
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 3px 10px rgba(59, 130, 246, 0.15);
  }
}

@keyframes darkGlow {
  0%, 100% {
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(71, 85, 105, 0.4);
  }
  50% {
    box-shadow: 
      0 8px 25px rgba(0, 0, 0, 0.4),
      0 4px 12px rgba(16, 185, 129, 0.2),
      0 0 0 1px rgba(16, 185, 129, 0.6);
  }
}
