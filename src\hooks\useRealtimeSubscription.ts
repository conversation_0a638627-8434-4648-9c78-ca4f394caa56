'use client';

import { useEffect, useRef, useCallback } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import type { RealtimeChannel } from '@supabase/supabase-js';

export type RealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';

export interface RealtimePayload<T = any> {
  eventType: RealtimeEvent;
  new: T | null;
  old: T | null;
  table: string;
}

export interface UseRealtimeSubscriptionOptions {
  table: string;
  filter?: string;
  event?: RealtimeEvent | RealtimeEvent[];
  onInsert?: (payload: RealtimePayload) => void;
  onUpdate?: (payload: RealtimePayload) => void;
  onDelete?: (payload: RealtimePayload) => void;
  onChange?: (payload: RealtimePayload) => void;
  enabled?: boolean;
}

export interface UseRealtimeSubscriptionReturn {
  isConnected: boolean;
  error: string | null;
  unsubscribe: () => void;
}

/**
 * Custom hook for Supabase real-time subscriptions
 * Provides live updates from database changes with proper cleanup
 */
export function useRealtimeSubscription(
  options: UseRealtimeSubscriptionOptions
): UseRealtimeSubscriptionReturn {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const isConnectedRef = useRef(false);
  const errorRef = useRef<string | null>(null);

  const {
    table,
    filter,
    event = ['INSERT', 'UPDATE', 'DELETE'],
    onInsert,
    onUpdate,
    onDelete,
    onChange,
    enabled = true
  } = options;

  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      channelRef.current.unsubscribe();
      channelRef.current = null;
      isConnectedRef.current = false;
    }
  }, []);

  const setupSubscription = useCallback(async () => {
    if (!enabled) return;

    try {
      // Clean up existing subscription
      unsubscribe();

      const supabase = await getSupabaseClient();
      
      // Create channel name
      const channelName = `${table}_changes_${Date.now()}`;
      const channel = supabase.channel(channelName);

      // Configure the subscription
      let subscription = channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: table,
          ...(filter && { filter })
        },
        (payload: any) => {
          const eventType = payload.eventType as RealtimeEvent;
          const realtimePayload: RealtimePayload = {
            eventType,
            new: payload.new || null,
            old: payload.old || null,
            table
          };

          // Call specific event handlers
          switch (eventType) {
            case 'INSERT':
              onInsert?.(realtimePayload);
              break;
            case 'UPDATE':
              onUpdate?.(realtimePayload);
              break;
            case 'DELETE':
              onDelete?.(realtimePayload);
              break;
          }

          // Call general change handler
          onChange?.(realtimePayload);
        }
      );

      // Subscribe to the channel
      subscription.subscribe((status: string) => {
        console.log(`Realtime subscription status for ${table}:`, status);
        
        if (status === 'SUBSCRIBED') {
          isConnectedRef.current = true;
          errorRef.current = null;
        } else if (status === 'CHANNEL_ERROR') {
          isConnectedRef.current = false;
          errorRef.current = `Failed to subscribe to ${table} changes`;
        } else if (status === 'TIMED_OUT') {
          isConnectedRef.current = false;
          errorRef.current = `Subscription to ${table} timed out`;
        }
      });

      channelRef.current = channel;
    } catch (error) {
      console.error('Failed to setup realtime subscription:', error);
      errorRef.current = error instanceof Error ? error.message : 'Unknown subscription error';
      isConnectedRef.current = false;
    }
  }, [table, filter, event, onInsert, onUpdate, onDelete, onChange, enabled, unsubscribe]);

  // Setup subscription on mount and when dependencies change
  useEffect(() => {
    setupSubscription();

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [setupSubscription, unsubscribe]);

  return {
    isConnected: isConnectedRef.current,
    error: errorRef.current,
    unsubscribe
  };
}

/**
 * Convenience hook for subscribing to student changes
 */
export function useStudentsRealtime(
  onStudentChange?: (payload: RealtimePayload) => void,
  filters?: { classId?: string; sectionId?: string }
) {
  const filter = filters?.classId 
    ? `class_id=eq.${filters.classId}${filters.sectionId ? ` and section_id=eq.${filters.sectionId}` : ''}`
    : filters?.sectionId 
    ? `section_id=eq.${filters.sectionId}`
    : undefined;

  return useRealtimeSubscription({
    table: 'students',
    filter,
    onChange: onStudentChange
  });
}

/**
 * Convenience hook for subscribing to master data changes
 */
export function useMasterDataRealtime(
  onMasterDataChange?: (payload: RealtimePayload) => void
) {
  const classesSubscription = useRealtimeSubscription({
    table: 'classes',
    onChange: onMasterDataChange
  });

  const sectionsSubscription = useRealtimeSubscription({
    table: 'sections',
    onChange: onMasterDataChange
  });

  const academicYearsSubscription = useRealtimeSubscription({
    table: 'academic_years',
    onChange: onMasterDataChange
  });

  const guardianRelationsSubscription = useRealtimeSubscription({
    table: 'guardian_relations',
    onChange: onMasterDataChange
  });

  return {
    classes: classesSubscription,
    sections: sectionsSubscription,
    academicYears: academicYearsSubscription,
    guardianRelations: guardianRelationsSubscription,
    unsubscribeAll: () => {
      classesSubscription.unsubscribe();
      sectionsSubscription.unsubscribe();
      academicYearsSubscription.unsubscribe();
      guardianRelationsSubscription.unsubscribe();
    }
  };
}
