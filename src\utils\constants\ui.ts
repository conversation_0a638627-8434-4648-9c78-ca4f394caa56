// src/utils/constants/ui.ts
// UI constants for consistent design system

/**
 * Color palette
 */
export const COLORS = {
  PRIMARY: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  SECONDARY: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  SUCCESS: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  WARNING: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  ERROR: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
} as const;

/**
 * Typography scale
 */
export const TYPOGRAPHY = {
  FONT_SIZES: {
    'xs-app': '0.75rem',    // 12px
    'sm-app': '0.875rem',   // 14px
    'base-app': '1rem',     // 16px
    'lg-app': '1.125rem',   // 18px
    'xl-app': '1.25rem',    // 20px
    '2xl-app': '1.5rem',    // 24px
    '3xl-app': '1.875rem',  // 30px
    '4xl-app': '2.25rem',   // 36px
    '5xl-app': '3rem',      // 48px
  },
  FONT_WEIGHTS: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  LINE_HEIGHTS: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
} as const;

/**
 * Spacing scale
 */
export const SPACING = {
  0: '0',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem',     // 96px
  32: '8rem',     // 128px
} as const;

/**
 * Border radius scale
 */
export const BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',   // 2px
  DEFAULT: '0.25rem', // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

/**
 * Shadow scale
 */
export const SHADOWS = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: '0 0 #0000',
} as const;

/**
 * Component sizes
 */
export const COMPONENT_SIZES = {
  BUTTON: {
    sm: {
      padding: '0.5rem 0.75rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['sm-app'],
      height: '2rem',
    },
    md: {
      padding: '0.625rem 1rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['base-app'],
      height: '2.5rem',
    },
    lg: {
      padding: '0.75rem 1.25rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['lg-app'],
      height: '3rem',
    },
  },
  INPUT: {
    sm: {
      padding: '0.5rem 0.75rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['sm-app'],
      height: '2rem',
    },
    md: {
      padding: '0.625rem 0.75rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['base-app'],
      height: '2.5rem',
    },
    lg: {
      padding: '0.75rem 1rem',
      fontSize: TYPOGRAPHY.FONT_SIZES['lg-app'],
      height: '3rem',
    },
  },
  CARD: {
    sm: {
      padding: SPACING[4],
      borderRadius: BORDER_RADIUS.md,
    },
    md: {
      padding: SPACING[6],
      borderRadius: BORDER_RADIUS.lg,
    },
    lg: {
      padding: SPACING[8],
      borderRadius: BORDER_RADIUS.xl,
    },
  },
  MODAL: {
    sm: {
      width: '20rem',
      maxWidth: '90vw',
    },
    md: {
      width: '28rem',
      maxWidth: '90vw',
    },
    lg: {
      width: '36rem',
      maxWidth: '90vw',
    },
    xl: {
      width: '48rem',
      maxWidth: '95vw',
    },
  },
} as const;

/**
 * Animation durations
 */
export const ANIMATIONS = {
  DURATION: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  EASING: {
    ease: 'ease',
    'ease-in': 'ease-in',
    'ease-out': 'ease-out',
    'ease-in-out': 'ease-in-out',
  },
} as const;

/**
 * Z-index scale
 */
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

/**
 * Breakpoints for responsive design
 */
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

/**
 * Layout constants
 */
export const LAYOUT = {
  SIDEBAR_WIDTH: '16rem',
  SIDEBAR_COLLAPSED_WIDTH: '4rem',
  HEADER_HEIGHT: '4rem',
  FOOTER_HEIGHT: '3rem',
  CONTAINER_MAX_WIDTH: '1200px',
} as const;

/**
 * Icon sizes
 */
export const ICON_SIZES = {
  xs: '0.75rem',   // 12px
  sm: '1rem',      // 16px
  md: '1.25rem',   // 20px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '2.5rem', // 40px
} as const;

/**
 * Common CSS classes for consistent styling
 */
export const CSS_CLASSES = {
  // Text styles
  TEXT_PRIMARY: 'text-gray-900',
  TEXT_SECONDARY: 'text-gray-600',
  TEXT_MUTED: 'text-gray-500',
  TEXT_ERROR: 'text-red-600',
  TEXT_SUCCESS: 'text-green-600',
  TEXT_WARNING: 'text-yellow-600',
  
  // Background styles
  BG_PRIMARY: 'bg-blue-600',
  BG_SECONDARY: 'bg-gray-600',
  BG_SUCCESS: 'bg-green-600',
  BG_WARNING: 'bg-yellow-600',
  BG_ERROR: 'bg-red-600',
  BG_SURFACE: 'bg-white',
  BG_MUTED: 'bg-gray-50',
  
  // Border styles
  BORDER_DEFAULT: 'border border-gray-300',
  BORDER_ERROR: 'border border-red-300',
  BORDER_SUCCESS: 'border border-green-300',
  
  // Focus styles
  FOCUS_RING: 'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
  FOCUS_OUTLINE: 'focus:outline-none',
  
  // Hover styles
  HOVER_BG: 'hover:bg-gray-50',
  HOVER_TEXT: 'hover:text-gray-900',
  
  // Transition styles
  TRANSITION_DEFAULT: 'transition-colors duration-200',
  TRANSITION_ALL: 'transition-all duration-200',
} as const;

/**
 * Helper function to get responsive classes
 */
export function getResponsiveClasses(
  base: string,
  sm?: string,
  md?: string,
  lg?: string,
  xl?: string
): string {
  const classes = [base];
  if (sm) classes.push(`sm:${sm}`);
  if (md) classes.push(`md:${md}`);
  if (lg) classes.push(`lg:${lg}`);
  if (xl) classes.push(`xl:${xl}`);
  return classes.join(' ');
}

/**
 * Helper function to get component size classes
 */
export function getComponentSizeClasses(
  component: keyof typeof COMPONENT_SIZES,
  size: keyof typeof COMPONENT_SIZES[keyof typeof COMPONENT_SIZES]
): string {
  const sizeConfig = COMPONENT_SIZES[component][size];
  return Object.entries(sizeConfig)
    .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
    .join('; ');
}

/**
 * Type definitions
 */
export type ColorShade = keyof typeof COLORS.PRIMARY;
export type FontSize = keyof typeof TYPOGRAPHY.FONT_SIZES;
export type FontWeight = keyof typeof TYPOGRAPHY.FONT_WEIGHTS;
export type SpacingValue = keyof typeof SPACING;
export type BorderRadiusValue = keyof typeof BORDER_RADIUS;
export type ShadowValue = keyof typeof SHADOWS;
export type ComponentSize = 'sm' | 'md' | 'lg';
export type Breakpoint = keyof typeof BREAKPOINTS;
