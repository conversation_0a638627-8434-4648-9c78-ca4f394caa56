// src/utils/constants/database.ts
// Centralized database configuration and constants

/**
 * Database table names
 * Single source of truth for all table references
 */
export const DATABASE_TABLES = {
  // Authentication & Users
  PROFILES: 'profiles',
  
  // Academic Structure
  CLASSES: 'classes',
  SECTIONS: 'sections',
  ACADEMIC_YEARS: 'academic_years',
  
  // Student Management
  STUDENTS: 'students',
  GUARDIAN_RELATIONS: 'guardian_relations',
  
  // Future Expansion (commented out until implemented)
  // TEACHERS: 'teachers',
  // SUBJECTS: 'subjects',
  // ATTENDANCE: 'attendance',
  // FEES: 'fees',
  // ASSIGNMENTS: 'assignments',
  // GRADES: 'grades',
  // ANNOUNCEMENTS: 'announcements',
} as const;

/**
 * Database schemas
 */
export const DATABASE_SCHEMAS = {
  PUBLIC: 'public',
  AUTH: 'auth',
} as const;

/**
 * Storage buckets for file uploads
 */
export const STORAGE_BUCKETS = {
  STUDENT_DOCUMENTS: 'student-documents',
  PROFILE_IMAGES: 'profile-images',
  ACADEMIC_DOCUMENTS: 'academic-documents',
} as const;

/**
 * Database column names for type-safe queries
 */
export const DATABASE_COLUMNS = {
  STUDENTS: {
    ID: 'id',
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    DATE_OF_BIRTH: 'date_of_birth',
    GENDER: 'gender',
    EMAIL: 'email',
    PHONE_NUMBER: 'phone_number',
    ADDRESS: 'address',
    GUARDIAN_NAME: 'guardian_name',
    GUARDIAN_RELATION_ID: 'guardian_relation_id',
    GUARDIAN_PHONE: 'guardian_phone',
    GUARDIAN_EMAIL: 'guardian_email',
    GUARDIAN_ADDRESS: 'guardian_address',
    EMERGENCY_CONTACT: 'emergency_contact',
    CLASS_ID: 'class_id',
    SECTION_ID: 'section_id',
    ROLL_NUMBER: 'roll_number',
    PREVIOUS_SCHOOL: 'previous_school',
    ACADEMIC_YEAR_ID: 'academic_year_id',
    PROFILE_ID: 'profile_id',
    BIRTH_CERTIFICATE_URL: 'birth_certificate_url',
    PREVIOUS_RECORDS_URL: 'previous_records_url',
    MEDICAL_RECORDS_URL: 'medical_records_url',
    PHOTOGRAPH_URL: 'photograph_url',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
  CLASSES: {
    ID: 'id',
    NAME: 'name',
    DESCRIPTION: 'description',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
  SECTIONS: {
    ID: 'id',
    NAME: 'name',
    DESCRIPTION: 'description',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
  ACADEMIC_YEARS: {
    ID: 'id',
    YEAR: 'year',
    START_DATE: 'start_date',
    END_DATE: 'end_date',
    IS_ACTIVE: 'is_active',
    IS_CURRENT: 'is_current',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
  GUARDIAN_RELATIONS: {
    ID: 'id',
    NAME: 'name',
    DESCRIPTION: 'description',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
  PROFILES: {
    ID: 'id',
    EMAIL: 'email',
    FULL_NAME: 'full_name',
    ROLE: 'role',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },
} as const;

/**
 * Common database query filters
 */
export const DATABASE_FILTERS = {
  ACTIVE_ONLY: { is_active: true },
  INACTIVE_ONLY: { is_active: false },
  CURRENT_ACADEMIC_YEAR: { is_current: true },
} as const;

/**
 * Database query limits
 */
export const DATABASE_LIMITS = {
  DEFAULT_PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 1000,
  SEARCH_RESULTS: 100,
} as const;

/**
 * Database sort orders
 */
export const DATABASE_SORT_ORDERS = {
  STUDENTS: {
    BY_NAME: [{ column: 'first_name', ascending: true }, { column: 'last_name', ascending: true }],
    BY_ROLL_NUMBER: [{ column: 'roll_number', ascending: true }],
    BY_CREATED_DATE: [{ column: 'created_at', ascending: false }],
  },
  CLASSES: {
    BY_NAME: [{ column: 'name', ascending: true }],
  },
  SECTIONS: {
    BY_NAME: [{ column: 'name', ascending: true }],
  },
  ACADEMIC_YEARS: {
    BY_YEAR: [{ column: 'year', ascending: false }],
    BY_START_DATE: [{ column: 'start_date', ascending: false }],
  },
} as const;

/**
 * Validation constants
 */
export const DATABASE_VALIDATION = {
  STUDENT: {
    FIRST_NAME_MAX_LENGTH: 50,
    LAST_NAME_MAX_LENGTH: 50,
    EMAIL_MAX_LENGTH: 255,
    PHONE_MAX_LENGTH: 20,
    ROLL_NUMBER_MAX_LENGTH: 20,
    ADDRESS_MAX_LENGTH: 500,
  },
  CLASS: {
    NAME_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 500,
  },
  SECTION: {
    NAME_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 500,
  },
} as const;

/**
 * Helper function to get table name safely
 */
export function getTableName(table: keyof typeof DATABASE_TABLES): string {
  return DATABASE_TABLES[table];
}

/**
 * Helper function to get column name safely
 */
export function getColumnName(
  table: keyof typeof DATABASE_COLUMNS,
  column: keyof typeof DATABASE_COLUMNS[keyof typeof DATABASE_COLUMNS]
): string {
  return DATABASE_COLUMNS[table][column] as string;
}

/**
 * Helper function to build select query with joins
 */
export function buildStudentSelectQuery(): string {
  return `
    *,
    class:${DATABASE_TABLES.CLASSES}(id, name),
    section:${DATABASE_TABLES.SECTIONS}(id, name),
    academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year),
    guardian_relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name),
    profile:${DATABASE_TABLES.PROFILES}(id, full_name, email)
  `;
}

/**
 * Type-safe table name type
 */
export type TableName = typeof DATABASE_TABLES[keyof typeof DATABASE_TABLES];

/**
 * Type-safe schema name type
 */
export type SchemaName = typeof DATABASE_SCHEMAS[keyof typeof DATABASE_SCHEMAS];

/**
 * Type-safe bucket name type
 */
export type BucketName = typeof STORAGE_BUCKETS[keyof typeof STORAGE_BUCKETS];
