// src/components/layout/theme-switcher.tsx
'use client';

import { useState, useEffect } from 'react';

interface ThemeSwitcherProps {
  className?: string;
}

const ThemeSwitcher = ({ className = '' }: ThemeSwitcherProps) => {
  const [currentTheme, setCurrentTheme] = useState<'default' | 'lighter' | 'darker'>('default');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Apply theme class to document root
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('layout-theme-lighter', 'layout-theme-darker');
    
    // Apply new theme class
    if (currentTheme === 'lighter') {
      root.classList.add('layout-theme-lighter');
    } else if (currentTheme === 'darker') {
      root.classList.add('layout-theme-darker');
    }
  }, [currentTheme]);

  const themes = [
    { id: 'default', name: 'Default Theme', description: 'Standard dark blue theme' },
    { id: 'lighter', name: 'Lighter Theme', description: 'Lighter slate theme' },
    { id: 'darker', name: 'Darker Theme', description: 'Darker slate theme' },
  ] as const;

  const handleThemeChange = (themeId: 'default' | 'lighter' | 'darker') => {
    setCurrentTheme(themeId);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Theme Switcher Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
        title="Switch Layout Theme"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
        </svg>
        <span className="text-sm font-medium">Theme</span>
        <svg className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Theme Dropdown */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white/95 backdrop-blur-md border border-white/30 rounded-lg shadow-xl z-50">
          <div className="p-3">
            <h3 className="text-sm font-semibold text-gray-800 mb-2">Layout Theme</h3>
            <div className="space-y-2">
              {themes.map((theme) => (
                <button
                  key={theme.id}
                  onClick={() => handleThemeChange(theme.id)}
                  className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                    currentTheme === theme.id
                      ? 'bg-blue-100 border-2 border-blue-300 text-blue-800'
                      : 'bg-gray-50 border-2 border-transparent text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">{theme.name}</div>
                      <div className="text-xs opacity-75">{theme.description}</div>
                    </div>
                    {currentTheme === theme.id && (
                      <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </button>
              ))}
            </div>
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="text-xs text-gray-600">
                Changes apply instantly to sidebar, header, and footer components using centralized CSS variables.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ThemeSwitcher;
