// src/app/student-management/page.tsx
'use client';

import { useState } from 'react';
import StudentList from './_components/student-list';

export default function StudentManagementPage() {
  const [showAddStudent, setShowAddStudent] = useState(false);

  const handleAddNewStudent = () => {
    setShowAddStudent(true);
    // TODO: Implement add student modal or navigation
    console.log('Add new student clicked');
  };

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Current Students</h1>
          <p className="text-gray-600 dark:text-gray-400">View and manage current enrolled students</p>
        </div>

        <StudentList onAddNewStudent={handleAddNewStudent} />
      </div>
    </div>
  );
}
