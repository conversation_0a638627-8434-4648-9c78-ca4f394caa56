// src/utils/types/index.ts
// Centralized exports for all type definitions

// Common types
export * from './common';

// Re-export Supabase types for convenience
export type {
  Database,
  Profile,
  Class,
  Section,
  AcademicYear,
  GuardianRelation,
  Student,
  StudentWithRelations,
  ProfileInsert,
  ClassInsert,
  SectionInsert,
  AcademicYearInsert,
  GuardianRelationInsert,
  StudentInsert,
  ProfileUpdate,
  ClassUpdate,
  SectionUpdate,
  AcademicYearUpdate,
  GuardianRelationUpdate,
  StudentUpdate,
  EnrollmentData
} from '@/lib/supabase/types';

// Re-export auth types
export type { User } from '@/utils/auth';
