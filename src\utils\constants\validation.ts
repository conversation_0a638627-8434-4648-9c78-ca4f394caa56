// src/utils/constants/validation.ts
// Validation rules, patterns, and constants

/**
 * Regular expression patterns for validation
 */
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  PHONE_INDIAN: /^[6-9]\d{9}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  NAME: /^[a-zA-Z\s'-]{2,50}$/,
  ROLL_NUMBER: /^[A-Z0-9]{1,20}$/,
  POSTAL_CODE: /^\d{6}$/,
  YEAR: /^\d{4}$/,
  DATE: /^\d{4}-\d{2}-\d{2}$/,
  TIME: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  NUMERIC: /^\d+$/,
  DECIMAL: /^\d+(\.\d{1,2})?$/,
} as const;

/**
 * Field length constraints
 */
export const FIELD_LENGTHS = {
  FIRST_NAME: { min: 2, max: 50 },
  LAST_NAME: { min: 2, max: 50 },
  EMAIL: { min: 5, max: 255 },
  PASSWORD: { min: 8, max: 128 },
  PHONE: { min: 10, max: 15 },
  ROLL_NUMBER: { min: 1, max: 20 },
  ADDRESS: { min: 10, max: 500 },
  DESCRIPTION: { min: 0, max: 1000 },
  CLASS_NAME: { min: 1, max: 100 },
  SECTION_NAME: { min: 1, max: 50 },
  GUARDIAN_NAME: { min: 2, max: 100 },
  EMERGENCY_CONTACT: { min: 10, max: 15 },
  PREVIOUS_SCHOOL: { min: 2, max: 200 },
} as const;

/**
 * Validation error messages
 */
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, and number',
  INVALID_NAME: 'Name can only contain letters, spaces, hyphens, and apostrophes',
  INVALID_ROLL_NUMBER: 'Roll number can only contain letters and numbers',
  INVALID_DATE: 'Please enter a valid date',
  INVALID_URL: 'Please enter a valid URL',
  TOO_SHORT: (field: string, min: number) => `${field} must be at least ${min} characters`,
  TOO_LONG: (field: string, max: number) => `${field} must not exceed ${max} characters`,
  INVALID_RANGE: (field: string, min: number, max: number) => 
    `${field} must be between ${min} and ${max} characters`,
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_DATE_RANGE: 'End date must be after start date',
  FUTURE_DATE_NOT_ALLOWED: 'Future dates are not allowed',
  PAST_DATE_NOT_ALLOWED: 'Past dates are not allowed',
  INVALID_AGE: (min: number, max: number) => `Age must be between ${min} and ${max} years`,
  FILE_TOO_LARGE: (maxSize: string) => `File size must not exceed ${maxSize}`,
  INVALID_FILE_TYPE: (allowedTypes: string) => `Only ${allowedTypes} files are allowed`,
} as const;

/**
 * Age validation constants
 */
export const AGE_LIMITS = {
  STUDENT: { min: 3, max: 25 },
  TEACHER: { min: 18, max: 70 },
  GUARDIAN: { min: 18, max: 100 },
} as const;

/**
 * Date validation constants
 */
export const DATE_LIMITS = {
  MIN_BIRTH_YEAR: 1950,
  MAX_BIRTH_YEAR: new Date().getFullYear(),
  MIN_ACADEMIC_YEAR: 2000,
  MAX_ACADEMIC_YEAR: new Date().getFullYear() + 5,
} as const;

/**
 * File validation constants
 */
export const FILE_VALIDATION = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
  MAX_FILES: 5,
} as const;

/**
 * Gender options
 */
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
] as const;

/**
 * Role options
 */
export const ROLE_OPTIONS = [
  { value: 'student', label: 'Student' },
  { value: 'teacher', label: 'Teacher' },
  { value: 'admin', label: 'Administrator' },
  { value: 'parent', label: 'Parent/Guardian' },
] as const;

/**
 * Common validation functions
 */
export const VALIDATORS = {
  email: (value: string): boolean => VALIDATION_PATTERNS.EMAIL.test(value),
  phone: (value: string): boolean => VALIDATION_PATTERNS.PHONE.test(value),
  indianPhone: (value: string): boolean => VALIDATION_PATTERNS.PHONE_INDIAN.test(value),
  password: (value: string): boolean => VALIDATION_PATTERNS.PASSWORD.test(value),
  name: (value: string): boolean => VALIDATION_PATTERNS.NAME.test(value),
  rollNumber: (value: string): boolean => VALIDATION_PATTERNS.ROLL_NUMBER.test(value),
  postalCode: (value: string): boolean => VALIDATION_PATTERNS.POSTAL_CODE.test(value),
  year: (value: string): boolean => VALIDATION_PATTERNS.YEAR.test(value),
  date: (value: string): boolean => VALIDATION_PATTERNS.DATE.test(value),
  url: (value: string): boolean => VALIDATION_PATTERNS.URL.test(value),
  
  // Length validators
  minLength: (value: string, min: number): boolean => value.length >= min,
  maxLength: (value: string, max: number): boolean => value.length <= max,
  lengthRange: (value: string, min: number, max: number): boolean => 
    value.length >= min && value.length <= max,
  
  // Age validators
  isValidAge: (birthDate: string, minAge: number, maxAge: number): boolean => {
    const birth = new Date(birthDate);
    const today = new Date();
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1 >= minAge && age - 1 <= maxAge;
    }
    
    return age >= minAge && age <= maxAge;
  },
  
  // Date validators
  isFutureDate: (date: string): boolean => new Date(date) > new Date(),
  isPastDate: (date: string): boolean => new Date(date) < new Date(),
  isDateInRange: (date: string, minDate: string, maxDate: string): boolean => {
    const checkDate = new Date(date);
    return checkDate >= new Date(minDate) && checkDate <= new Date(maxDate);
  },
  
  // File validators
  isValidFileSize: (file: File, maxSize: number = FILE_VALIDATION.MAX_SIZE): boolean => 
    file.size <= maxSize,
  isValidFileType: (file: File, allowedTypes: string[]): boolean => 
    allowedTypes.includes(file.type),
} as const;

/**
 * Helper function to get validation message
 */
export function getValidationMessage(
  field: string,
  rule: keyof typeof VALIDATION_MESSAGES,
  ...args: any[]
): string {
  const message = VALIDATION_MESSAGES[rule];
  if (typeof message === 'function') {
    return message(field, ...args);
  }
  return message;
}

/**
 * Helper function to validate field length
 */
export function validateFieldLength(
  value: string,
  field: keyof typeof FIELD_LENGTHS
): { isValid: boolean; message?: string } {
  const limits = FIELD_LENGTHS[field];
  
  if (value.length < limits.min) {
    return {
      isValid: false,
      message: getValidationMessage(field, 'TOO_SHORT', limits.min)
    };
  }
  
  if (value.length > limits.max) {
    return {
      isValid: false,
      message: getValidationMessage(field, 'TOO_LONG', limits.max)
    };
  }
  
  return { isValid: true };
}

/**
 * Type definitions
 */
export type ValidationPattern = typeof VALIDATION_PATTERNS[keyof typeof VALIDATION_PATTERNS];
export type ValidationMessage = typeof VALIDATION_MESSAGES[keyof typeof VALIDATION_MESSAGES];
export type GenderOption = typeof GENDER_OPTIONS[number];
export type RoleOption = typeof ROLE_OPTIONS[number];
