// src/lib/supabase/index.ts
// Centralized exports for all Supabase utilities

// Core client exports
export { getSupabaseClient, supabase } from './client';

// Authentication exports - explicitly export what we need to avoid name conflicts
export {
    getSupabaseSession // Renamed to avoid conflicts with session.ts
    , onAuthStateChange, signIn,
    signOut, signUp
} from './auth';

// Session management exports
export {
    createSession,
    deleteSession, getSession as getJWTSession // Export with a different name to avoid conflicts
    , verifySession
} from './session';

// Data Access Layer exports
export * from './dal';

// Type exports
export * from './types';

// Middleware export (for convenience, though it's typically imported directly)
export { default as middleware } from './middleware';
