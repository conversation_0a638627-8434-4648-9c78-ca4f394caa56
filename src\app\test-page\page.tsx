// src/app/test-page/page.tsx
'use client';

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Test Page</h1>
        <p className="text-gray-600 mb-6">This is a simple test page to verify routing works.</p>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Page Information</h2>
          <ul className="space-y-2">
            <li><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</li>
            <li><strong>Pathname:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'Loading...'}</li>
            <li><strong>Timestamp:</strong> {new Date().toLocaleString()}</li>
          </ul>
        </div>
        
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Navigation Test</h3>
          <div className="space-x-4">
            <a href="/student-management/current" className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              Go to Current Students
            </a>
            <a href="/dashboard" className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
              Go to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
