/* ===================================================================
   CENTRALIZED LAYOUT THEME MANAGEMENT SYSTEM
   ===================================================================
   This file contains all CSS custom properties (variables) for the
   application layout components: sidebar, header, and footer.
   
   Changing values here will automatically update all three components.
   =================================================================== */

:root {
  /* ===================================================================
     SIDEBAR THEME VARIABLES
     =================================================================== */
  
  /* Primary Background Gradients - Light Blue Glass-like Effects */
  --sidebar-bg-primary: linear-gradient(135deg,
    #1e293b 0%,                      /* Slate 800 - Deep dark base */
    #334155 20%,                     /* Slate 700 - Medium dark */
    #374151 40%,                     /* Gray 700 - Rich dark center */
    #334155 60%,                     /* Slate 700 - Medium dark */
    #1e293b 100%                     /* Slate 800 - Deep dark base */
  );
  
  --sidebar-header-bg: linear-gradient(135deg,
    #1e293b 0%,                      /* Slate 800 - Deep dark */
    #334155 30%,                     /* Slate 700 - Medium dark */
    #374151 50%,                     /* Gray 700 - Rich center */
    #334155 70%,                     /* Slate 700 - Medium dark */
    #1e293b 100%                     /* Slate 800 - Deep dark */
  );
  
  /* Navigation Item Colors */
  --sidebar-nav-text: #cbd5e1;           /* Slate 300 - Default text */
  --sidebar-nav-text-hover: #ffffff;     /* White - Hover text */
  --sidebar-nav-text-active: #ffffff;    /* White - Active text */
  
  /* Navigation Item Background Colors */
  --sidebar-nav-bg-hover: linear-gradient(135deg,
    rgba(71, 85, 105, 0.4) 0%,
    rgba(51, 65, 85, 0.3) 50%,
    rgba(71, 85, 105, 0.4) 100%
  );
  
  --sidebar-nav-bg-active: linear-gradient(135deg,
    rgba(16, 185, 129, 0.25) 0%,     /* Emerald accent */
    rgba(34, 197, 94, 0.3) 25%,      /* Green accent */
    rgba(59, 130, 246, 0.25) 50%,    /* Blue accent center */
    rgba(34, 197, 94, 0.3) 75%,      /* Green accent */
    rgba(16, 185, 129, 0.25) 100%    /* Emerald accent */
  );
  
  /* Active State Colors - Green as preferred */
  --sidebar-active-accent: #10b981;      /* Emerald 500 - Primary active color */
  --sidebar-active-accent-light: #34d399; /* Emerald 400 - Light active color */
  --sidebar-active-accent-dark: #059669;  /* Emerald 600 - Dark active color */
  
  /* Sub-navigation Colors */
  --sidebar-subnav-text: #94a3b8;        /* Slate 400 - Default sub-nav text */
  --sidebar-subnav-text-hover: #6ee7b7;  /* Emerald 300 - Hover sub-nav text */
  --sidebar-subnav-text-active: #a7f3d0; /* Emerald 200 - Active sub-nav text */
  
  --sidebar-subnav-bg-hover: linear-gradient(135deg,
    rgba(71, 85, 105, 0.4) 0%,
    rgba(51, 65, 85, 0.45) 100%
  );
  
  --sidebar-subnav-bg-active: linear-gradient(135deg,
    rgba(16, 185, 129, 0.2) 0%,
    rgba(34, 197, 94, 0.15) 50%,
    rgba(6, 182, 212, 0.1) 100%
  );
  
  /* Icon Colors */
  --sidebar-icon-default: #cbd5e1;       /* Slate 300 - Default icon color */
  --sidebar-icon-hover: #ffffff;         /* White - Hover icon color */
  --sidebar-icon-active: #a7f3d0;        /* Emerald 200 - Active icon color */
  --sidebar-subicon-default: #64748b;    /* Slate 500 - Default sub-icon color */
  --sidebar-subicon-hover: #4ade80;      /* Emerald 400 - Hover sub-icon color */
  --sidebar-subicon-active: #6ee7b7;     /* Emerald 300 - Active sub-icon color */
  
  /* Brand Colors */
  --sidebar-brand-gradient: linear-gradient(135deg, #10b981 0%, #34d399 40%, #6ee7b7 80%, #a7f3d0 100%);
  --sidebar-brand-shadow: rgba(16, 185, 129, 0.3);
  
  /* Border and Shadow Colors */
  --sidebar-border: rgba(71, 85, 105, 0.5); /* Slate 600/50 */
  --sidebar-separator-gradient: linear-gradient(180deg,
    rgba(16, 185, 129, 0.8) 0%,
    rgba(34, 197, 94, 0.9) 25%,
    rgba(59, 130, 246, 0.8) 50%,
    rgba(34, 197, 94, 0.9) 75%,
    rgba(16, 185, 129, 0.8) 100%
  );
  
  /* Profile Section Colors */
  --sidebar-profile-text: #cbd5e1;       /* Slate 300 - Profile text */
  --sidebar-profile-text-hover: #ffffff; /* White - Profile hover text */
  --sidebar-profile-bg-hover: linear-gradient(135deg,
    rgba(71, 85, 105, 0.6) 0%,
    rgba(51, 65, 85, 0.4) 50%,
    rgba(71, 85, 105, 0.6) 100%
  );
  
  /* Tooltip Colors */
  --sidebar-tooltip-bg: linear-gradient(135deg,
    rgba(30, 41, 59, 0.95) 0%,       /* Dark slate */
    rgba(51, 65, 85, 0.9) 50%,       /* Medium slate */
    rgba(30, 41, 59, 0.95) 100%      /* Dark slate */
  );
  --sidebar-tooltip-text: #ffffff;
  --sidebar-tooltip-border: rgba(51, 65, 85, 0.98);
  
  /* Collapse Toggle Colors */
  --sidebar-toggle-bg: linear-gradient(to right, #059669, #0d9488); /* Emerald 600 to Teal 600 */
  --sidebar-toggle-bg-hover: linear-gradient(to right, #10b981, #14b8a6); /* Emerald 500 to Teal 500 */
  --sidebar-toggle-shadow: rgba(16, 185, 129, 0.4);
  --sidebar-toggle-shadow-hover: rgba(16, 185, 129, 0.6);
  
  /* ===================================================================
     HEADER THEME VARIABLES
     =================================================================== */
  
  /* Header Background - Matching Sidebar Gradient/Colors */
  --header-bg-primary: linear-gradient(135deg,
    #1e293b 0%,                      /* Slate 800 - Deep dark to match sidebar */
    #334155 30%,                     /* Slate 700 - Medium dark */
    #374151 50%,                     /* Gray 700 - Rich center */
    #334155 70%,                     /* Slate 700 - Medium dark */
    #1e293b 100%                     /* Slate 800 - Deep dark to match sidebar */
  );
  
  /* Header Text Colors */
  --header-title-text: #ffffff;          /* White - Header title */
  --header-breadcrumb-text: #cbd5e1;     /* Slate 300 - Breadcrumb text */
  --header-breadcrumb-current: #ffffff;  /* White - Current breadcrumb */
  
  /* Header Profile Colors */
  --header-profile-name: #ffffff;        /* White - Profile name */
  --header-profile-role: #cbd5e1;        /* Slate 300 - Profile role */
  --header-profile-avatar-bg: linear-gradient(to bottom right, #10b981, #0d9488); /* Emerald to Teal */
  
  /* Header Action Button Colors */
  --header-action-btn-text: #cbd5e1;     /* Slate 300 - Action button text */
  --header-action-btn-text-hover: #ffffff; /* White - Action button hover text */
  --header-action-btn-bg-hover: linear-gradient(135deg,
    rgba(71, 85, 105, 0.4) 0%,
    rgba(51, 65, 85, 0.3) 50%,
    rgba(71, 85, 105, 0.4) 100%
  );
  
  /* Header Dropdown Colors */
  --header-dropdown-bg: linear-gradient(135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.9) 50%,
    rgba(30, 41, 59, 0.95) 100%
  );
  --header-dropdown-text: #cbd5e1;       /* Slate 300 - Dropdown text */
  --header-dropdown-text-hover: #ffffff; /* White - Dropdown hover text */
  --header-dropdown-border: rgba(71, 85, 105, 0.5);
  
  /* Header Border and Shadow */
  --header-border: rgba(71, 85, 105, 0.5); /* Slate 600/50 */
  --header-shadow: 0 3px 12px rgba(0, 0, 0, 0.3), 0 1px 6px rgba(0, 0, 0, 0.2);
  
  /* ===================================================================
     FOOTER THEME VARIABLES
     =================================================================== */
  
  /* Footer Background - Matching Sidebar/Header Theme */
  --footer-bg-primary: linear-gradient(to right, #1e293b, #374151, #1e293b); /* Slate 800 via Gray 700 to Slate 800 */
  
  /* Footer Text Colors */
  --footer-text-primary: #cbd5e1;        /* Slate 300 - Primary footer text */
  --footer-text-secondary: #94a3b8;      /* Slate 400 - Secondary footer text */
  --footer-link-text: #cbd5e1;           /* Slate 300 - Footer link text */
  --footer-link-text-hover: #ffffff;     /* White - Footer link hover text */
  
  /* Footer Border */
  --footer-border: rgba(71, 85, 105, 0.5); /* Slate 600/50 */
  
  /* ===================================================================
     SHARED LAYOUT VARIABLES
     =================================================================== */
  
  /* Common Backdrop Filter */
  --layout-backdrop-filter: blur(24px) saturate(1.1);
  --layout-backdrop-filter-light: blur(16px) saturate(1.1);
  --layout-backdrop-filter-heavy: blur(32px) saturate(1.2);
  
  /* Common Transition Durations */
  --layout-transition-fast: 0.2s;
  --layout-transition-normal: 0.3s;
  --layout-transition-slow: 0.4s;
  
  /* Common Border Radius */
  --layout-border-radius-sm: 0.5rem;     /* 8px */
  --layout-border-radius-md: 0.75rem;    /* 12px */
  --layout-border-radius-lg: 1rem;       /* 16px */
  --layout-border-radius-xl: 1.25rem;    /* 20px */
  
  /* Common Shadow Levels */
  --layout-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --layout-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --layout-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --layout-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

/* ===================================================================
   THEME VARIANT CLASSES
   ===================================================================
   These classes can be applied to switch between theme variations
   =================================================================== */

/* Lighter Theme Variation */
.layout-theme-lighter {
  --sidebar-bg-primary: linear-gradient(135deg,
    #334155 0%,                      /* Slate 700 - Lighter base */
    #475569 20%,                     /* Slate 600 - Medium */
    #64748b 40%,                     /* Slate 500 - Center */
    #475569 60%,                     /* Slate 600 - Medium */
    #334155 100%                     /* Slate 700 - Lighter base */
  );
  
  --header-bg-primary: linear-gradient(135deg,
    #334155 0%,
    #475569 30%,
    #64748b 50%,
    #475569 70%,
    #334155 100%
  );
  
  --footer-bg-primary: linear-gradient(to right, #334155, #64748b, #334155);
}

/* Darker Theme Variation */
.layout-theme-darker {
  --sidebar-bg-primary: linear-gradient(135deg,
    #0f172a 0%,                      /* Slate 900 - Darker base */
    #1e293b 20%,                     /* Slate 800 - Medium dark */
    #334155 40%,                     /* Slate 700 - Center */
    #1e293b 60%,                     /* Slate 800 - Medium dark */
    #0f172a 100%                     /* Slate 900 - Darker base */
  );
  
  --header-bg-primary: linear-gradient(135deg,
    #0f172a 0%,
    #1e293b 30%,
    #334155 50%,
    #1e293b 70%,
    #0f172a 100%
  );
  
  --footer-bg-primary: linear-gradient(to right, #0f172a, #334155, #0f172a);
}

/* ===================================================================
   SIDEBAR COMPONENT CLASSES
   =================================================================== */

.sidebar-themed {
  background: var(--sidebar-bg-primary);
  border-color: var(--sidebar-border);
  backdrop-filter: var(--layout-backdrop-filter);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(15, 23, 42, 0.4);
  position: relative;
}

.sidebar-themed::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background: var(--sidebar-separator-gradient);
  transition: opacity var(--layout-transition-normal) ease;
  animation: separatorGlow 4s ease-in-out infinite;
  border-radius: 0 2px 2px 0;
}

.sidebar-themed:hover::after {
  animation-duration: 2s;
}

.sidebar-themed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.3) 0%,
    rgba(15, 23, 42, 0.1) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.sidebar-header-themed {
  border-color: var(--sidebar-border);
  background: var(--sidebar-header-bg);
  backdrop-filter: var(--layout-backdrop-filter);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 1px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(15, 23, 42, 0.4);
  position: relative;
  z-index: 2;
}

.sidebar-brand-themed {
  font-weight: bold;
  background: var(--sidebar-brand-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px var(--sidebar-brand-shadow));
}

.sidebar-nav-item-themed {
  color: var(--sidebar-nav-text);
  transition: all var(--layout-transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0;
  border-radius: var(--layout-border-radius-md);
  backdrop-filter: var(--layout-backdrop-filter-light);
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(71, 85, 105, 0.3),
    inset 0 -1px 0 rgba(15, 23, 42, 0.2);
  position: relative;
  z-index: 2;
}

.sidebar-nav-item-themed:hover {
  color: var(--sidebar-nav-text-hover);
  background: var(--sidebar-nav-bg-hover);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 3px 10px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(71, 85, 105, 0.6),
    inset 0 -1px 0 rgba(15, 23, 42, 0.3);
  transform: translateY(-2px);
}

.sidebar-nav-item-themed::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, transparent 0%, var(--sidebar-active-accent-light) 50%, transparent 100%);
  opacity: 0;
  transform: scaleY(0);
  border-radius: 0 var(--layout-border-radius-sm) var(--layout-border-radius-sm) 0;
  transition: all var(--layout-transition-normal);
}

.sidebar-nav-item-themed:hover::before {
  opacity: 1;
  transform: scaleY(1);
}

.sidebar-nav-item-themed.active {
  color: var(--sidebar-nav-text-active);
  background: var(--sidebar-nav-bg-active);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 4px 12px rgba(16, 185, 129, 0.2),
    0 2px 8px rgba(34, 197, 94, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(15, 23, 42, 0.3);
  position: relative;
  z-index: 3;
}

.sidebar-nav-item-themed.active::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.3) 0%,
    rgba(34, 197, 94, 0.2) 100%
  );
  border-radius: calc(var(--layout-border-radius-md) + 2px);
  z-index: -1;
  filter: blur(4px);
}

.sidebar-label-themed {
  color: var(--sidebar-nav-text);
  font-weight: 500;
  font-size: 0.875rem;
}

.sidebar-nav-item-themed.active .sidebar-label-themed {
  color: var(--sidebar-nav-text-active);
  font-weight: 600;
  text-shadow: 0 1px 2px var(--sidebar-brand-shadow);
}

.sidebar-subitem-themed {
  color: var(--sidebar-subnav-text);
  transition: all var(--layout-transition-normal);
  margin: 0.25rem 0 0.25rem 1.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--layout-border-radius-sm);
  backdrop-filter: var(--layout-backdrop-filter-light);
  z-index: 1;
}

.sidebar-subitem-themed:hover {
  color: var(--sidebar-subnav-text-hover);
  background: var(--sidebar-subnav-bg-hover);
  transform: translateX(2px);
}

.sidebar-subitem-themed.active {
  color: var(--sidebar-subnav-text-active);
  background: var(--sidebar-subnav-bg-active);
  font-weight: 500;
  text-shadow: 0 1px 2px var(--sidebar-brand-shadow);
}

.sidebar-icon-themed {
  color: var(--sidebar-icon-default);
  transition: color var(--layout-transition-normal);
  width: 1.25rem;
  height: 1.25rem;
}

.sidebar-nav-item-themed:hover .sidebar-icon-themed {
  color: var(--sidebar-icon-hover);
}

.sidebar-nav-item-themed.active .sidebar-icon-themed {
  color: var(--sidebar-icon-active);
  filter: drop-shadow(0 1px 2px var(--sidebar-brand-shadow));
}

.sidebar-subicon-themed {
  color: var(--sidebar-subicon-default);
  transition: color var(--layout-transition-normal);
  opacity: 0.7;
}

.sidebar-subitem-themed:hover .sidebar-subicon-themed {
  color: var(--sidebar-subicon-hover);
  opacity: 1;
}

.sidebar-subitem-themed.active .sidebar-subicon-themed {
  color: var(--sidebar-subicon-active);
  opacity: 1;
  filter: drop-shadow(0 1px 2px var(--sidebar-brand-shadow));
}

.sidebar-tooltip-themed {
  position: absolute;
  left: 100%;
  margin-left: 1rem;
  padding: 0.5rem 0.75rem;
  color: var(--sidebar-tooltip-text);
  font-size: 0.875rem;
  border-radius: var(--layout-border-radius-md);
  box-shadow: var(--layout-shadow-xl);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-50%) translateX(-4px);
  transition: all var(--layout-transition-normal) ease-out;
  background: var(--sidebar-tooltip-bg);
  backdrop-filter: var(--layout-backdrop-filter);
  border: 1px solid var(--sidebar-border);
  top: 50%;
  z-index: 9999;
  pointer-events: none;
}

.sidebar-tooltip-themed::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right: 6px solid var(--sidebar-tooltip-border);
  filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.2));
}

.sidebar-tooltip-themed.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(4px);
  animation: tooltipSlideIn var(--layout-transition-normal) ease-out forwards;
}

.sidebar-collapse-toggle-themed {
  position: absolute;
  top: 50%;
  background: var(--sidebar-toggle-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--layout-transition-normal);
  box-shadow: 0 4px 12px var(--sidebar-toggle-shadow);
  width: 2.5rem;
  height: 2.5rem;
  border: 2px solid var(--sidebar-border);
  z-index: 9999;
}

.sidebar-collapse-toggle-themed:hover {
  background: var(--sidebar-toggle-bg-hover);
  transform: scale(1.1);
  box-shadow: 0 0 1.5625rem var(--sidebar-toggle-shadow-hover);
}

.sidebar-collapse-toggle-themed svg {
  width: 1rem;
  height: 1rem;
  color: white;
  transition: transform var(--layout-transition-normal);
}

.sidebar-collapse-toggle-themed.collapsed svg {
  transform: rotate(180deg);
}

/* Collapsed sidebar specific styles */
.sidebar-item-collapsed-themed {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--layout-border-radius-xl);
  transition: all var(--layout-transition-normal);
  color: var(--sidebar-nav-text);
  width: 3rem;
  height: 3rem;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--sidebar-border);
  backdrop-filter: var(--layout-backdrop-filter-light);
}

.sidebar-item-collapsed-themed:hover {
  background: var(--sidebar-nav-bg-hover);
  border-color: var(--sidebar-active-accent);
  transform: scale(1.05);
  box-shadow: 0 4px 15px var(--sidebar-toggle-shadow);
}

.sidebar-item-collapsed-themed.active {
  color: var(--sidebar-nav-text-active);
  background: var(--sidebar-nav-bg-active);
  border-color: var(--sidebar-active-accent);
  box-shadow: 0 4px 15px var(--sidebar-toggle-shadow-hover);
}

@keyframes separatorGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(4px);
  }
}

/* ===================================================================
   HEADER COMPONENT CLASSES
   =================================================================== */

.header-themed {
  border-color: var(--header-border);
  background: var(--header-bg-primary);
  backdrop-filter: var(--layout-backdrop-filter);
  box-shadow: var(--header-shadow);
  position: relative;
}

.header-title-themed {
  color: var(--header-title-text);
  font-size: 1.25rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-breadcrumb-themed {
  color: var(--header-breadcrumb-text);
  font-size: 0.875rem;
}

.header-breadcrumb-current-themed {
  color: var(--header-breadcrumb-current);
  font-weight: 500;
}

.header-profile-name-themed {
  color: var(--header-profile-name);
  font-size: 0.875rem;
  font-weight: 500;
}

.header-profile-role-themed {
  color: var(--header-profile-role);
  font-size: 0.75rem;
}

.header-profile-avatar-themed {
  background: var(--header-profile-avatar-bg);
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--layout-shadow-lg);
}

.header-action-btn-themed {
  color: var(--header-action-btn-text);
  transition: all var(--layout-transition-normal);
  padding: 0.5rem;
  border-radius: var(--layout-border-radius-md);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-action-btn-themed:hover {
  color: var(--header-action-btn-text-hover);
  background: var(--header-action-btn-bg-hover);
}

.header-dropdown-themed {
  position: absolute;
  right: 0;
  margin-top: 0.5rem;
  width: 12rem;
  border-radius: var(--layout-border-radius-md);
  box-shadow: var(--layout-shadow-xl);
  border: 1px solid var(--header-dropdown-border);
  padding: 0.25rem 0;
  z-index: 50;
  background: var(--header-dropdown-bg);
  backdrop-filter: var(--layout-backdrop-filter);
}

.header-dropdown-item-themed {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: var(--header-dropdown-text);
  transition: all var(--layout-transition-fast);
}

.header-dropdown-item-themed:hover {
  color: var(--header-dropdown-text-hover);
  background: var(--header-action-btn-bg-hover);
}

.header-dropdown-item-themed.danger {
  color: #ef4444; /* Red 500 for danger actions */
}

.header-dropdown-item-themed.danger:hover {
  color: #dc2626; /* Red 600 for danger hover */
  background: rgba(239, 68, 68, 0.1);
}

/* ===================================================================
   FOOTER COMPONENT CLASSES
   =================================================================== */

.footer-themed {
  background: var(--footer-bg-primary);
  border-top: 1px solid var(--footer-border);
  padding: 1rem 1.5rem;
  backdrop-filter: var(--layout-backdrop-filter-light);
}

.footer-text-primary-themed {
  color: var(--footer-text-primary);
  font-size: 0.875rem;
}

.footer-text-secondary-themed {
  color: var(--footer-text-secondary);
  font-size: 0.875rem;
}

.footer-link-themed {
  color: var(--footer-link-text);
  font-size: 0.875rem;
  transition: color var(--layout-transition-fast);
}

.footer-link-themed:hover {
  color: var(--footer-link-text-hover);
}

/* ===================================================================
   RESPONSIVE UTILITIES
   =================================================================== */

@media (max-width: 768px) {
  .sidebar-themed {
    width: 4rem;
  }

  .sidebar-nav-item-themed {
    padding: 0.75rem;
    justify-content: center;
  }

  .sidebar-label-themed {
    display: none;
  }

  .header-breadcrumb-themed {
    display: none;
  }
}

/* ===================================================================
   UTILITY CLASSES FOR LAYOUT COMPONENTS
   =================================================================== */

.layout-height-header {
  height: 3.5rem; /* 56px height for compact header */
}

.layout-transition-all {
  transition: all var(--layout-transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.layout-glass-effect {
  backdrop-filter: var(--layout-backdrop-filter);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.layout-shadow-glow {
  box-shadow:
    var(--layout-shadow-lg),
    0 0 20px rgba(16, 185, 129, 0.1);
}
