import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ClientProviders } from '../components/providers/client-providers';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'EduPro - Education Management System',
  description: 'Comprehensive education management system for modern educational institutions',
  keywords: 'school management, education, student management, enrollment',
  authors: [{ name: 'EduPro Team' }],
  openGraph: {
    title: 'EduPro - Education Management System',
    description: 'Comprehensive education management system for modern educational institutions',
    type: 'website',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
