// src/lib/supabase/auth.ts
'use client';

import type { User } from '@/utils/auth';
import { getSupabaseClient } from './client';

export interface SupabaseAuthResponse {
  user: User | null;
  error: string | null;
  success: boolean;
}

/**
 * Sign up a new user with Supabase
 */
export async function signUp(email: string, password: string, userData: {
  name: string;
  role: string;
}): Promise<SupabaseAuthResponse> {
  try {
    const supabase = await getSupabaseClient();
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: userData.name,
          role: userData.role
        }
      }
    });

    if (error) {
      const userFriendlyMessage = error.message.includes('already registered') 
        ? 'An account with this email already exists. Please try signing in instead.'
        : error.message.includes('Invalid email')
        ? 'Please enter a valid email address.'
        : error.message.includes('Password')
        ? 'Password must be at least 6 characters long.'
        : 'Unable to create account. Please try again.';

      return {
        user: null,
        error: userFriendlyMessage,
        success: false
      };
    }

    if (data.user) {
      // Try to create profile in the profiles table, but handle gracefully if table doesn't exist
      try {
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email || '',
            full_name: userData.name,
            role: userData.role as 'student' | 'teacher' | 'admin' | 'parent'
          });

        if (profileError) {
          console.warn('Profile creation failed:', profileError);
        }
      } catch (profileError) {
        console.warn('Profiles table not accessible during signup:', profileError);
      }

      const user: User = {
        name: userData.name,
        email: data.user.email || '',
        role: userData.role,
        isAuthenticated: true
      };

      return {
        user,
        error: null,
        success: true
      };
    }

    return {
      user: null,
      error: 'Account creation failed. Please try again.',
      success: false
    };
  } catch (error) {
    return {
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      success: false
    };
  }
}

/**
 * Sign in an existing user with Supabase
 */
export async function signIn(email: string, password: string): Promise<SupabaseAuthResponse> {
  try {
    const supabase = await getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      const userFriendlyMessage = error.message.includes('Invalid login credentials')
        ? 'Invalid email or password. Please check your credentials and try again.'
        : error.message.includes('Email not confirmed')
        ? 'Please check your email and click the confirmation link before signing in.'
        : error.message.includes('Too many requests')
        ? 'Too many login attempts. Please wait a moment and try again.'
        : 'Unable to sign in. Please try again.';

      return {
        user: null,
        error: userFriendlyMessage,
        success: false
      };
    }

    if (data.user) {
      // Try to get user profile from database, but don't fail if table doesn't exist
      let profile = null;
      try {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (!profileError) {
          profile = profileData;
        }
      } catch (error) {
        // Profiles table might not exist yet, continue without it
        console.log('Profile lookup failed, using metadata:', error);
      }

      const userName = profile?.full_name || data.user.user_metadata?.full_name || data.user.email?.split('@')[0] || '';
      const userRole = profile?.role || data.user.user_metadata?.role || 'student';

      const user: User = {
        name: userName,
        email: data.user.email || '',
        role: userRole,
        isAuthenticated: true
      };

      return {
        user,
        error: null,
        success: true
      };
    }

    return {
      user: null,
      error: 'Sign in failed. Please try again.',
      success: false
    };
  } catch (error) {
    return {
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      success: false
    };
  }
}

/**
 * Sign out the current user
 */
export async function signOut(): Promise<void> {
  try {
    const supabase = await getSupabaseClient();
    await supabase.auth.signOut();
    clearUserLocally();
  } catch (_error) {
    // Clear local storage even if Supabase signOut fails
    clearUserLocally();
  }
}

/**
 * Get the current authenticated user
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // Create a timeout promise that rejects after 500ms
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Auth check timeout')), 500);
    });

    // Create the session check promise
    const supabase = await getSupabaseClient();
    const sessionPromise = supabase.auth.getSession();

    // Race between session check and timeout
    const { data: { session }, error: sessionError } = await Promise.race([
      sessionPromise,
      timeoutPromise
    ]);

    if (sessionError || !session?.user) {
      return null;
    }

    // Try to get user profile, but don't fail if table doesn't exist
    let profile = null;
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (!profileError) {
        profile = profileData;
      }
    } catch (error) {
      // Continue without profile if table doesn't exist
      console.log('Profile lookup failed, using session data:', error);
    }

    const userName = profile?.full_name || session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || '';
    const userRole = profile?.role || session.user.user_metadata?.role || 'student';

    return {
      name: userName,
      email: session.user.email || '',
      role: userRole,
      isAuthenticated: true
    };
  } catch (error) {
    console.warn('getCurrentUser failed:', error);
    return null;
  }
}

/**
 * Renamed from getSession to getSupabaseSession to avoid conflicts with session.ts
 */
export async function getSupabaseSession() {
  try {
    const supabase = await getSupabaseClient();
    return supabase.auth.getSession();
  } catch (error) {
    console.error('Error getting Supabase session:', error);
    return { data: { session: null }, error };
  }
}

/**
 * Listen to auth state changes
 */
export async function onAuthStateChange(callback: (user: User | null) => void) {
  try {
    const supabase = await getSupabaseClient();
    return supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      // Only respond to specific auth events to avoid unnecessary state changes
      if (event === 'SIGNED_IN' && session?.user) {
        const user = await getCurrentUser();
        callback(user);
      } else if (event === 'SIGNED_OUT') {
        callback(null);
      }
      // Ignore other events like TOKEN_REFRESHED to prevent interference with session-based auth
    });
  } catch (error) {
    console.error('Auth state change listener error:', error);
    // Return a dummy subscription that can be safely unsubscribed
    return {
      data: {
        subscription: {
          unsubscribe: () => {}
        }
      }
    };
  }
}

/**
 * Clear user data from local storage
 */
function clearUserLocally() {
  if (typeof window !== 'undefined') {
    // Clear any local storage items if needed
    localStorage.removeItem('user');
    localStorage.removeItem('auth-token');
  }
}

/**
 * Test Supabase connection
 */
export async function testConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await getSupabaseClient();
    const { data, error } = await supabase.auth.getSession();
    console.log('Supabase connection test:', { data, error });

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
