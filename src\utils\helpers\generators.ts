// src/utils/helpers/generators.ts
// Utility functions for generating IDs, codes, and other values

/**
 * ID generation utilities
 */
export const idGenerators = {
  /**
   * Generate UUID v4
   */
  uuid: (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * Generate short ID
   */
  shortId: (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate numeric ID
   */
  numericId: (length: number = 6): string => {
    const chars = '0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate alphanumeric ID
   */
  alphanumericId: (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate timestamp-based ID
   */
  timestampId: (): string => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
};

/**
 * Code generation utilities
 */
export const codeGenerators = {
  /**
   * Generate student roll number
   */
  rollNumber: (classCode: string, year: number, sequence: number): string => {
    const yearSuffix = year.toString().slice(-2);
    const sequenceStr = sequence.toString().padStart(3, '0');
    return `${classCode}${yearSuffix}${sequenceStr}`;
  },

  /**
   * Generate admission number
   */
  admissionNumber: (year: number, sequence: number): string => {
    const yearStr = year.toString();
    const sequenceStr = sequence.toString().padStart(4, '0');
    return `ADM${yearStr}${sequenceStr}`;
  },

  /**
   * Generate employee ID
   */
  employeeId: (department: string, year: number, sequence: number): string => {
    const deptCode = department.substring(0, 3).toUpperCase();
    const yearSuffix = year.toString().slice(-2);
    const sequenceStr = sequence.toString().padStart(3, '0');
    return `${deptCode}${yearSuffix}${sequenceStr}`;
  },

  /**
   * Generate class code
   */
  classCode: (className: string): string => {
    return className
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 3)
      .toUpperCase();
  },

  /**
   * Generate section code
   */
  sectionCode: (sectionName: string): string => {
    return sectionName
      .replace(/[^a-zA-Z]/g, '')
      .substring(0, 1)
      .toUpperCase();
  },

  /**
   * Generate academic year code
   */
  academicYearCode: (startYear: number): string => {
    const endYear = startYear + 1;
    return `AY${startYear.toString().slice(-2)}${endYear.toString().slice(-2)}`;
  }
};

/**
 * Password generation utilities
 */
export const passwordGenerators = {
  /**
   * Generate random password
   */
  random: (length: number = 12): string => {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  },

  /**
   * Generate memorable password
   */
  memorable: (): string => {
    const adjectives = ['Quick', 'Bright', 'Happy', 'Swift', 'Bold', 'Smart', 'Cool', 'Fast'];
    const nouns = ['Tiger', 'Eagle', 'Lion', 'Wolf', 'Bear', 'Fox', 'Hawk', 'Shark'];
    const numbers = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    const symbols = ['!', '@', '#', '$', '%'];
    
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    
    return `${adjective}${noun}${numbers}${symbol}`;
  },

  /**
   * Generate PIN
   */
  pin: (length: number = 4): string => {
    let pin = '';
    for (let i = 0; i < length; i++) {
      pin += Math.floor(Math.random() * 10).toString();
    }
    return pin;
  }
};

/**
 * Color generation utilities
 */
export const colorGenerators = {
  /**
   * Generate random hex color
   */
  randomHex: (): string => {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
  },

  /**
   * Generate pastel color
   */
  pastel: (): string => {
    const hue = Math.floor(Math.random() * 360);
    return `hsl(${hue}, 70%, 85%)`;
  },

  /**
   * Generate color palette
   */
  palette: (baseColor: string, count: number = 5): string[] => {
    const colors: string[] = [];
    const baseHue = parseInt(baseColor.replace('#', ''), 16);
    
    for (let i = 0; i < count; i++) {
      const hue = (baseHue + (i * 360 / count)) % 360;
      colors.push(`hsl(${hue}, 70%, 60%)`);
    }
    
    return colors;
  },

  /**
   * Generate avatar background color
   */
  avatarColor: (name: string): string => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
};

/**
 * File name generation utilities
 */
export const fileNameGenerators = {
  /**
   * Generate unique file name
   */
  unique: (originalName: string): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const extension = originalName.split('.').pop();
    const baseName = originalName.replace(/\.[^/.]+$/, '');
    
    return `${baseName}_${timestamp}_${random}.${extension}`;
  },

  /**
   * Generate student document name
   */
  studentDocument: (studentId: string, documentType: string, originalName: string): string => {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop();
    return `student_${studentId}_${documentType}_${timestamp}.${extension}`;
  },

  /**
   * Generate export file name
   */
  export: (type: string, format: string = 'csv'): string => {
    const date = new Date().toISOString().split('T')[0];
    const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '');
    return `${type}_export_${date}_${time}.${format}`;
  },

  /**
   * Generate backup file name
   */
  backup: (type: string): string => {
    const date = new Date().toISOString().split('T')[0];
    const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '');
    return `backup_${type}_${date}_${time}.sql`;
  }
};

/**
 * URL generation utilities
 */
export const urlGenerators = {
  /**
   * Generate slug from text
   */
  slug: (text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  },

  /**
   * Generate API endpoint
   */
  apiEndpoint: (resource: string, id?: string, action?: string): string => {
    let endpoint = `/api/${resource}`;
    if (id) endpoint += `/${id}`;
    if (action) endpoint += `/${action}`;
    return endpoint;
  },

  /**
   * Generate query string
   */
  queryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }
};

/**
 * Token generation utilities
 */
export const tokenGenerators = {
  /**
   * Generate API token
   */
  apiToken: (): string => {
    const prefix = 'edup_';
    const token = idGenerators.alphanumericId(32);
    return `${prefix}${token}`;
  },

  /**
   * Generate session token
   */
  sessionToken: (): string => {
    return idGenerators.alphanumericId(64);
  },

  /**
   * Generate verification code
   */
  verificationCode: (length: number = 6): string => {
    return idGenerators.numericId(length);
  },

  /**
   * Generate reset token
   */
  resetToken: (): string => {
    return idGenerators.alphanumericId(48);
  }
};

/**
 * Test data generation utilities
 */
export const testDataGenerators = {
  /**
   * Generate random name
   */
  name: (): { firstName: string; lastName: string } => {
    const firstNames = [
      'Aarav', 'Vivaan', 'Aditya', 'Vihaan', 'Arjun', 'Sai', 'Reyansh', 'Ayaan',
      'Krishna', 'Ishaan', 'Ananya', 'Diya', 'Priya', 'Kavya', 'Aanya', 'Sara',
      'Ira', 'Tara', 'Aditi', 'Riya'
    ];
    
    const lastNames = [
      'Sharma', 'Verma', 'Gupta', 'Singh', 'Kumar', 'Agarwal', 'Jain', 'Patel',
      'Shah', 'Mehta', 'Reddy', 'Nair', 'Iyer', 'Rao', 'Pillai', 'Menon',
      'Bhat', 'Joshi', 'Pandey', 'Mishra'
    ];
    
    return {
      firstName: firstNames[Math.floor(Math.random() * firstNames.length)],
      lastName: lastNames[Math.floor(Math.random() * lastNames.length)]
    };
  },

  /**
   * Generate random email
   */
  email: (name?: { firstName: string; lastName: string }): string => {
    const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    
    if (name) {
      const username = `${name.firstName.toLowerCase()}.${name.lastName.toLowerCase()}`;
      return `${username}@${domain}`;
    }
    
    const username = idGenerators.alphanumericId(8).toLowerCase();
    return `${username}@${domain}`;
  },

  /**
   * Generate random phone
   */
  phone: (): string => {
    const prefixes = ['98', '97', '96', '95', '94', '93', '92', '91', '90', '89'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const number = idGenerators.numericId(8);
    return `${prefix}${number}`;
  },

  /**
   * Generate random date of birth
   */
  dateOfBirth: (minAge: number = 5, maxAge: number = 18): string => {
    const currentYear = new Date().getFullYear();
    const birthYear = currentYear - Math.floor(Math.random() * (maxAge - minAge + 1)) - minAge;
    const month = Math.floor(Math.random() * 12) + 1;
    const day = Math.floor(Math.random() * 28) + 1;
    
    return `${birthYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }
};
