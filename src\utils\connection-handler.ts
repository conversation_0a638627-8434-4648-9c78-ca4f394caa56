// src/utils/connection-handler.ts
'use client';

/**
 * Connection error handler for ECONNRESET and other network issues
 */

export interface ConnectionError extends Error {
  code?: string;
  digest?: string;
}

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
};

/**
 * Check if an error is a connection reset error
 */
export function isConnectionResetError(error: any): boolean {
  if (!error) return false;
  
  const errorCode = error.code || error.digest;
  const errorMessage = error.message || '';
  
  return (
    errorCode === 'ECONNRESET' ||
    errorCode === '3485663598' || // Specific digest from our error
    errorMessage.includes('ECONNRESET') ||
    errorMessage.includes('aborted') ||
    errorMessage.includes('connection reset')
  );
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  if (!error) return false;
  
  // Connection reset errors are retryable
  if (isConnectionResetError(error)) {
    return true;
  }
  
  const errorCode = error.code || error.status;
  const errorMessage = error.message || '';
  
  // Network errors that are retryable
  return (
    errorCode === 'NETWORK_ERROR' ||
    errorCode === 'TIMEOUT' ||
    errorCode === 408 || // Request Timeout
    errorCode === 429 || // Too Many Requests
    errorCode === 502 || // Bad Gateway
    errorCode === 503 || // Service Unavailable
    errorCode === 504 || // Gateway Timeout
    errorMessage.includes('fetch') ||
    errorMessage.includes('network') ||
    errorMessage.includes('timeout')
  );
}

/**
 * Calculate delay for retry with exponential backoff
 */
function calculateDelay(attempt: number, options: Required<RetryOptions>): number {
  const delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1);
  return Math.min(delay, options.maxDelay);
}

/**
 * Sleep for a specified duration
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry a function with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: Error;

  for (let attempt = 1; attempt <= opts.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry if it's not a retryable error
      if (!isRetryableError(error)) {
        throw lastError;
      }
      
      // Don't retry on the last attempt
      if (attempt === opts.maxRetries) {
        break;
      }
      
      const delay = calculateDelay(attempt, opts);
      console.log(`Retry attempt ${attempt}/${opts.maxRetries} after ${delay}ms delay due to:`, error);
      
      await sleep(delay);
    }
  }

  throw lastError!;
}

/**
 * Wrap a fetch request with connection error handling
 */
export async function safeFetch(
  input: RequestInfo | URL,
  init?: RequestInit,
  retryOptions?: RetryOptions
): Promise<Response> {
  return withRetry(async () => {
    try {
      const response = await fetch(input, init);
      
      // Check if response indicates a server error that should be retried
      if (response.status >= 500 && response.status < 600) {
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }
      
      return response;
    } catch (error) {
      // Convert fetch errors to our error format
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const connectionError: ConnectionError = new Error('Network connection failed');
        connectionError.code = 'NETWORK_ERROR';
        throw connectionError;
      }
      throw error;
    }
  }, retryOptions);
}

/**
 * Handle connection errors gracefully with user-friendly messages
 */
export function handleConnectionError(error: any): {
  message: string;
  isRetryable: boolean;
  shouldRedirect: boolean;
} {
  if (isConnectionResetError(error)) {
    return {
      message: 'Connection was interrupted. This is usually temporary and should resolve shortly.',
      isRetryable: true,
      shouldRedirect: false,
    };
  }
  
  if (isRetryableError(error)) {
    return {
      message: 'Network connection issue. Please check your internet connection and try again.',
      isRetryable: true,
      shouldRedirect: false,
    };
  }
  
  // Non-retryable errors
  return {
    message: 'An unexpected error occurred. Please refresh the page and try again.',
    isRetryable: false,
    shouldRedirect: true,
  };
}

/**
 * Create a connection-safe wrapper for async operations
 */
export function createConnectionSafeWrapper<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  retryOptions?: RetryOptions
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await withRetry(() => fn(...args), retryOptions);
    } catch (error) {
      const errorInfo = handleConnectionError(error);
      console.error('Connection-safe operation failed:', error, errorInfo);
      
      // Re-throw with additional context
      const enhancedError = error instanceof Error ? error : new Error(String(error));
      (enhancedError as any).connectionInfo = errorInfo;
      throw enhancedError;
    }
  }) as T;
}
