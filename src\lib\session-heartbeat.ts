// src/lib/session-heartbeat.ts
'use client';

/**
 * Session heartbeat mechanism to prevent unexpected logouts
 */

let heartbeatInterval: NodeJS.Timeout | null = null;
let isHeartbeatActive = false;
let isRefreshing = false; // Prevent concurrent refresh operations
let lastRefreshTime = 0; // Track last refresh to prevent too frequent calls

/**
 * Start session heartbeat to keep session alive
 */
export function startSessionHeartbeat(): void {
  // Don't start multiple heartbeats
  if (isHeartbeatActive) {
    console.log('Session heartbeat already active, skipping start');
    return;
  }

  console.log('Starting session heartbeat');
  isHeartbeatActive = true;

  // Check and refresh session every 30 minutes
  heartbeatInterval = setInterval(async () => {
    try {
      await refreshSessionIfNeeded();
    } catch (error) {
      console.error('Session heartbeat error:', error);
    }
  }, 30 * 60 * 1000); // 30 minutes

  // Also refresh on page visibility change (when user returns to tab)
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  // Refresh on page focus
  if (typeof window !== 'undefined') {
    window.addEventListener('focus', handleWindowFocus);
  }
}

/**
 * Stop session heartbeat
 */
export function stopSessionHeartbeat(): void {
  console.log('Stopping session heartbeat');

  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }

  isHeartbeatActive = false;
  isRefreshing = false; // Reset refresh state
  lastRefreshTime = 0; // Reset last refresh time

  // Remove event listeners
  if (typeof document !== 'undefined') {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  }

  if (typeof window !== 'undefined') {
    window.removeEventListener('focus', handleWindowFocus);
  }
}

/**
 * Handle page visibility change
 */
async function handleVisibilityChange(): Promise<void> {
  if (document.visibilityState === 'visible') {
    // Add debouncing to prevent too frequent calls
    const now = Date.now();
    if (now - lastRefreshTime < 5000) { // 5 second minimum between refreshes
      return;
    }

    try {
      await refreshSessionIfNeeded();
    } catch (error) {
      console.error('Visibility change session refresh error:', error);
    }
  }
}

/**
 * Handle window focus
 */
async function handleWindowFocus(): Promise<void> {
  // Add debouncing to prevent too frequent calls
  const now = Date.now();
  if (now - lastRefreshTime < 5000) { // 5 second minimum between refreshes
    return;
  }

  try {
    await refreshSessionIfNeeded();
  } catch (error) {
    console.error('Window focus session refresh error:', error);
  }
}

/**
 * Refresh session if it's close to expiring
 */
async function refreshSessionIfNeeded(): Promise<void> {
  // Prevent concurrent refresh operations
  if (isRefreshing) {
    console.log('Session refresh already in progress, skipping');
    return;
  }

  try {
    isRefreshing = true;
    lastRefreshTime = Date.now();

    // Import session functions and connection handler dynamically
    const [{ getSession, refreshSession }, { withRetry, isConnectionResetError }] = await Promise.all([
      import('../utils/supabase/session'),
      import('../utils/connection-handler')
    ]);

    const session = await getSession();

    if (!session) {
      return;
    }

    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
    const refreshThreshold = 2 * 60 * 60 * 1000; // Refresh if less than 2 hours remaining

    if (timeUntilExpiry < refreshThreshold && timeUntilExpiry > 0) {
      console.log('Refreshing session due to heartbeat check');

      // Use retry logic for session refresh
      await withRetry(
        () => refreshSession(),
        { maxRetries: 2, baseDelay: 1000 }
      );

      console.log('Session refresh completed successfully');
    }
  } catch (error) {
    // Import connection handler to check error type
    try {
      const { isConnectionResetError } = await import('../utils/connection-handler');
      if (isConnectionResetError(error)) {
        console.warn('Session refresh failed due to connection reset, will retry on next heartbeat:', error);
      } else {
        console.error('Session refresh check failed:', error);
      }
    } catch {
      console.error('Session refresh check failed:', error);
    }
    // Don't throw error to prevent breaking the heartbeat
  } finally {
    isRefreshing = false;
  }
}

/**
 * Check if session heartbeat is active
 */
export function isSessionHeartbeatActive(): boolean {
  return isHeartbeatActive;
}

/**
 * Force session refresh
 */
export async function forceSessionRefresh(): Promise<boolean> {
  // Prevent concurrent refresh operations
  if (isRefreshing) {
    console.log('Session refresh already in progress, cannot force refresh');
    return false;
  }

  try {
    isRefreshing = true;
    lastRefreshTime = Date.now();

    const { refreshSession } = await import('../utils/supabase/session');
    await refreshSession();
    console.log('Force session refresh completed successfully');
    return true;
  } catch (error) {
    console.error('Force session refresh failed:', error);
    return false;
  } finally {
    isRefreshing = false;
  }
}

/**
 * Get session status information
 */
export async function getSessionStatus(): Promise<{
  exists: boolean;
  valid: boolean;
  expiresAt: string | null;
  timeUntilExpiry: number | null;
  needsRefresh: boolean;
}> {
  try {
    const { getSession, verifySession } = await import('../utils/supabase/session');

    const session = await getSession();
    const user = await verifySession();
    
    if (!session) {
      return {
        exists: false,
        valid: false,
        expiresAt: null,
        timeUntilExpiry: null,
        needsRefresh: false,
      };
    }

    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
    const refreshThreshold = 2 * 60 * 60 * 1000; // 2 hours

    return {
      exists: true,
      valid: !!user,
      expiresAt: session.expiresAt.toString(),
      timeUntilExpiry,
      needsRefresh: timeUntilExpiry < refreshThreshold && timeUntilExpiry > 0,
    };
  } catch (error) {
    console.error('Get session status failed:', error);
    return {
      exists: false,
      valid: false,
      expiresAt: null,
      timeUntilExpiry: null,
      needsRefresh: false,
    };
  }
}
