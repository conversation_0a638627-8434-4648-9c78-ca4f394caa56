/*
===============================================================================
                    STUDENT WIZARD COMPONENT STYLES
===============================================================================

This file contains styles specific to the Add Student Wizard component.
It inherits from the global design system and ensures consistent typography
and styling across all wizard steps.

DESIGN PRINCIPLES:
- Inherit typography from globals.css design system
- Consistent form element styling across all steps
- Professional appearance with proper spacing
- Responsive design for mobile and desktop

===============================================================================
*/

/* Import global design system */
@import '../../../globals.css';

/* Wizard Container Styles */
.student-wizard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
}

.student-wizard::before {
  content: '';
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(0.25rem);
  -webkit-backdrop-filter: blur(0.25rem);
  z-index: -1;
}

/* Compact Modal Container */
.wizard-modal {
  background: white;
  border-radius: 1rem;
  box-shadow:
    0 1.5625rem 3.125rem -0.75rem rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(1rem);
  -webkit-backdrop-filter: blur(1rem);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transform: scale(1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 50rem; /* 800px converted to rem */
  width: 90vw; /* Keep viewport units for responsive design */
}

.wizard-modal:hover {
  box-shadow:
    0 2rem 4rem -0.75rem rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Compact Wizard Header Styles */
.wizard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
  padding: 1rem 1.5rem 0.75rem 1.5rem;
}

.wizard-header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 50%, rgba(240, 147, 251, 0.9) 100%);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
}

.wizard-header > * {
  position: relative;
  z-index: 1;
}

.wizard-title {
  @apply text-xl-app font-bold text-white;
  text-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
  margin-bottom: 0.5rem;
}

.wizard-step-indicator {
  @apply text-xs-app font-semibold text-white;
  text-shadow: 0 1px 0.125rem rgba(0, 0, 0, 0.1);
}

.wizard-step-description {
  display: none; /* Hide descriptions to save space */
}

/* Form Element Styles - Consistent Typography */
.wizard-form-label {
  @apply text-xs-app font-semibold text-gray-800 block;
  font-family: inherit;
  margin-bottom: 0.25rem;
}

.wizard-form-label.required::after {
  content: " *";
  @apply text-red-500;
}

.wizard-form-input {
  @apply w-full border border-gray-200/80 rounded-lg text-sm-app font-medium placeholder:text-gray-400 transition-all duration-300;
  font-family: inherit;
  height: 2.25rem; /* Keep rem for consistent scaling */
  padding: 0.5rem 0.75rem; /* Convert px to rem */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  box-shadow:
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-input:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 0.25rem 0.75rem rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-input:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 0.5rem 1.5625rem rgba(79, 70, 229, 0.15),
    0 0 0 0.25rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.wizard-form-input.error {
  @apply border-red-300/80 focus:ring-red-500/20 focus:border-red-500/60;
  background: rgba(254, 242, 242, 0.8);
}

.wizard-form-select {
  @apply w-full border border-gray-200/80 rounded-lg text-sm-app font-medium transition-all duration-300 appearance-none;
  font-family: inherit;
  height: 2.25rem; /* Keep rem for consistent scaling */
  padding: 0.5rem 0.75rem; /* Convert px to rem */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2.5rem;
  box-shadow:
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-select:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 0.25rem 0.75rem rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-select:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 0.5rem 1.5625rem rgba(79, 70, 229, 0.15),
    0 0 0 0.25rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.wizard-form-textarea {
  @apply w-full border border-gray-200/80 rounded-lg resize-none text-sm-app font-medium placeholder:text-gray-400 transition-all duration-300;
  font-family: inherit;
  min-height: 3.5rem; /* Keep rem for consistent scaling */
  padding: 0.5rem 0.75rem; /* Convert px to rem */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  box-shadow:
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-textarea:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 0.25rem 0.75rem rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-textarea:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 0.5rem 1.5625rem rgba(79, 70, 229, 0.15),
    0 0 0 0.25rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* Date Input Specific Styles */
.wizard-form-date {
  @apply wizard-form-input text-sm-app;
}

/* Error Message Styles */
.wizard-form-error {
  @apply text-sm-app text-red-600 flex items-center;
  font-family: inherit;
  margin-top: 0.25rem; /* Convert px to rem */
}

/* Modern Button Styles */
.wizard-btn {
  @apply font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden;
  font-family: inherit;
  position: relative;
  transform: translateY(0);
}

.wizard-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wizard-btn:hover::before {
  opacity: 1;
}

.wizard-btn-primary {
  @apply wizard-btn text-white text-sm-app;
  padding: 0.5rem 1.5rem; /* Convert px to rem */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow:
    0 0.5rem 1.5625rem rgba(102, 126, 234, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-primary:hover {
  transform: translateY(-0.125rem);
  box-shadow:
    0 0.75rem 2.1875rem rgba(102, 126, 234, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.wizard-btn-secondary {
  @apply wizard-btn text-gray-700 text-sm-app;
  padding: 0.5rem 1rem; /* Convert px to rem */
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(209, 213, 219, 0.8);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  box-shadow:
    0 0.25rem 0.75rem rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-secondary:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(156, 163, 175, 0.8);
  box-shadow:
    0 0.375rem 1rem rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-btn-success {
  @apply wizard-btn text-white text-sm-app;
  padding: 0.5rem 1.5rem; /* Convert px to rem */
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow:
    0 0.5rem 1.5625rem rgba(16, 185, 129, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-success:hover {
  transform: translateY(-0.125rem);
  box-shadow:
    0 0.75rem 2.1875rem rgba(16, 185, 129, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.wizard-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

.wizard-btn:disabled::before {
  opacity: 0 !important;
}

/* Step Content Styles */
.wizard-step-content {
  @apply w-full max-w-none mx-auto;
  gap: 1.5rem; /* Convert space-y-6 to gap in rem */
  display: flex;
  flex-direction: column;
}

.wizard-form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2;
  gap: 1rem; /* Convert gap-4 to rem */
}

.wizard-form-grid.three-cols {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  gap: 1rem; /* Convert gap-4 to rem */
}

.wizard-form-group {
  gap: 0.25rem; /* Convert space-y-1 to gap in rem */
  display: flex;
  flex-direction: column;
}

.wizard-form-group.full-width {
  @apply md:col-span-2 lg:col-span-2;
}

.wizard-form-group.full-width-three {
  @apply md:col-span-2 lg:col-span-3;
}

/* Optimized spacing for form sections */
.wizard-form-section {
  margin-bottom: 1.25rem; /* Convert mb-5 to rem */
}

.wizard-form-section:last-child {
  margin-bottom: 0;
}

/* Compact File Upload Styles */
.wizard-file-upload {
  @apply border-2 border-dashed rounded-lg text-center cursor-pointer transition-all duration-300 relative overflow-hidden;
  padding: 0.75rem; /* Convert p-3 to rem */
  border-color: rgba(209, 213, 219, 0.6);
  background: rgba(249, 250, 251, 0.5);
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  box-shadow:
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-file-upload::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wizard-file-upload:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background: rgba(79, 70, 229, 0.05);
  transform: translateY(-0.125rem);
  box-shadow:
    0 0.5rem 1.5625rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-file-upload:hover::before {
  opacity: 1;
}

.wizard-file-upload-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center mx-auto relative z-10 transition-all duration-300;
  margin-bottom: 0.5rem; /* Convert mb-2 to rem */
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(79, 70, 229, 0.05));
  box-shadow:
    0 0.25rem 0.75rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-file-upload:hover .wizard-file-upload-icon {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(79, 70, 229, 0.1));
  transform: scale(1.1);
  box-shadow:
    0 0.375rem 1rem rgba(79, 70, 229, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-file-upload-text {
  @apply text-sm-app font-semibold text-gray-700 relative z-10;
  font-family: inherit;
}

.wizard-file-upload-subtext {
  @apply text-xs-app text-gray-500 font-medium relative z-10;
  font-family: inherit;
}

/* Compact Horizontal Step Progress Styles */
.wizard-step-progress {
  @apply flex items-center justify-center;
  margin-top: 0.75rem; /* Convert mt-3 to rem */
  padding: 0 1rem; /* Convert px-4 to rem */
  gap: 0.5rem;
}

.wizard-step-item {
  @apply flex items-center;
  position: relative;
}

.wizard-step-circle {
  @apply w-8 h-8 rounded-full flex items-center justify-center font-bold text-xs relative z-10 transition-all duration-300;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
}

.wizard-step-circle.active {
  @apply bg-white text-indigo-600;
  box-shadow:
    0 0.25rem 0.75rem rgba(79, 70, 229, 0.3),
    0 0 0 0.125rem rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.wizard-step-circle.completed {
  @apply bg-gradient-to-br from-green-500 to-emerald-600 text-white;
  box-shadow:
    0 0.25rem 0.75rem rgba(34, 197, 94, 0.3),
    0 0 0 0.125rem rgba(34, 197, 94, 0.1);
  transform: scale(1.05);
}

.wizard-step-circle.inactive {
  @apply bg-white/30 text-white/70 backdrop-blur-sm;
  box-shadow: 0 1px 0.25rem rgba(0, 0, 0, 0.1);
}

.wizard-step-content {
  margin-left: 0.5rem; /* Convert ml-2 to rem */
  
}

.wizard-step-line {
  @apply h-0.5 bg-gradient-to-r flex-1 relative;
  margin: 0 0.5rem; /* Convert mx-2 to rem */
  
}

.wizard-step-line.completed {
  @apply from-white to-white;
  box-shadow: 0 0 0.25rem rgba(255, 255, 255, 0.3);
}

.wizard-step-line.incomplete {
  @apply from-white/30 to-white/30;
}

.wizard-step-line::before {
  content: '';
  position: absolute;
  inset: 0;
  background: inherit;
  border-radius: 2px;
  filter: blur(1px);
}

/* Step Icons for Completed Steps */
.wizard-step-circle.completed .step-number {
  display: none;
}

.wizard-step-circle.completed::after {
  content: '✓';
  font-size: 0.75rem; /* Convert 12px to rem */
  font-weight: bold;
}

/* Compact Content Container */
.wizard-content {
  @apply overflow-y-auto max-h-[65vh];
  padding: 1.25rem; /* Convert p-5 to rem */
  background: linear-gradient(to bottom, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.8));
  position: relative;
}

.wizard-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(79, 70, 229, 0.2), transparent);
}

/* Compact Footer */
.wizard-footer {
  @apply border-t border-gray-100;
  padding: 0.75rem; /* Convert p-3 to rem */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(249, 250, 251, 0.9));
  backdrop-filter: blur(0.5rem);
  -webkit-backdrop-filter: blur(0.5rem);
  position: relative;
}

.wizard-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(79, 70, 229, 0.1), transparent);
}

.wizard-footer-actions {
  @apply flex items-center justify-between;
  position: relative;
  z-index: 1;
}

/* Loading States */
.wizard-loading {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .wizard-modal {
    margin: 1rem;
    max-height: 95vh;
  }

  .wizard-header {
    padding: 1rem;
  }

  .wizard-title {
    @apply text-xl-app;
  }

  .wizard-step-progress {
    margin-top: 1rem; /* Convert mt-4 to rem */
    padding: 0 0.5rem; /* Convert px-2 to rem */
    gap: 0.5rem;
  }

  .wizard-step-circle {
    @apply w-10 h-10 text-xs-app;
  }

  .wizard-step-content {
    @apply hidden;
  }

  .wizard-step-line {
    min-width: 1.25rem; /* Convert 20px to rem */
    max-width: 2.5rem; /* Convert 40px to rem */
  }

  .wizard-content {
    padding: 1rem; /* Convert p-4 to rem */
  }

  .wizard-form-grid,
  .wizard-form-grid.three-cols {
    @apply grid-cols-1;
    gap: 0.75rem; /* Convert gap-3 to rem */
  }

  .wizard-form-input,
  .wizard-form-select,
  .wizard-form-textarea {
    @apply text-xs-app;
    height: 2rem; /* Keep rem for consistent scaling */
  }

  .wizard-form-textarea {
    min-height: 3rem; /* Keep rem for consistent scaling */
  }

  .wizard-btn-primary,
  .wizard-btn-success {
    @apply text-xs-app;
    padding: 0.5rem 1rem; /* Convert px to rem */
  }

  .wizard-btn-secondary {
    @apply text-xs-app;
    padding: 0.5rem 0.75rem; /* Convert px to rem */
  }

  .wizard-footer {
    padding: 1rem; /* Convert p-4 to rem */
  }

  .wizard-footer-actions {
    @apply flex-col;
    gap: 0.75rem; /* Convert gap-3 to rem */
  }

  .wizard-footer-actions > * {
    @apply w-full;
  }
}

@media (min-width: 769px) and (max-width: 1023px) {
  .wizard-form-grid {
    @apply grid-cols-2;
    gap: 1rem; /* Convert gap-4 to rem */
  }
}

/* Focus and Accessibility */
.wizard-form-input:focus,
.wizard-form-select:focus,
.wizard-form-textarea:focus {
  @apply outline-none;
}

/* Validation States */
.wizard-form-input.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

.wizard-form-select.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

/* Modern Animation Classes */
.wizard-fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.wizard-modal {
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.wizard-step-content {
  animation: stepContentFadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1.25rem); /* Convert 20px to rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(1.25rem); /* Convert 20px to rem */
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes stepContentFadeIn {
  from {
    opacity: 0;
    transform: translateX(0.625rem); /* Convert 10px to rem */
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for active step */
@keyframes pulse {
  0%, 100% {
    box-shadow:
      0 0.5rem 1.5625rem rgba(79, 70, 229, 0.3),
      0 0 0 0.25rem rgba(79, 70, 229, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 0.5rem 1.5625rem rgba(79, 70, 229, 0.4),
      0 0 0 0.375rem rgba(79, 70, 229, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

.wizard-step-circle.active {
  animation: pulse 2s infinite;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
