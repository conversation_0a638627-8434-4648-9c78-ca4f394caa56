// src/components/layout/app-header.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useAuth } from '../auth/auth-provider';
import ThemeSwitcher from './theme-switcher';

interface AppHeaderProps {
  title: string;
  currentRoute?: string;
}

const AppHeader = ({ title, currentRoute = 'dashboard' }: AppHeaderProps) => {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const router = useRouter();
  const { user, signOut } = useAuth();

  const handleProfileNavigation = () => {
    router.push('/settings/profile'); // Navigate to profile settings page
    setIsProfileOpen(false);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsProfileOpen(false);
      router.push('/product');
    } catch (error) {
      console.error('Sign out error:', error);
      // Force redirect even if sign out fails
      router.push('/product');
    }
  };

  const getBreadcrumb = () => {
    const routeMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'student-management': 'Student Management',
    };

    const routeName = routeMap[currentRoute] || 'Dashboard';
    
    return (
      <nav className="header-breadcrumb-themed hidden md:flex items-center space-x-2 text-sm-app">
        <span>EduPro</span>
        <span>/</span>
        <span className="header-breadcrumb-current-themed">{routeName}</span>
        {title !== routeName && (
          <>
            <span>/</span>
            <span className="header-breadcrumb-current-themed">{title}</span>
          </>
        )}
      </nav>
    );
  };

  return (
    <header className="header-themed layout-height-header shadow-sm border-b px-4 py-2">
      <div className="flex items-center justify-between h-full">
        {/* Title and Breadcrumb */}
        <div className="flex items-center space-x-4">
          <h1 className="header-title-themed">{title}</h1>
          {getBreadcrumb()}
        </div>

        {/* Header Actions */}
        <div className="flex items-center space-x-4">
          {/* Theme Switcher */}
          <ThemeSwitcher />

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="header-action-btn-themed focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            >
              <div className="header-profile-avatar-themed">
                <span className="text-white text-sm-app font-medium">U</span>
              </div>
              <div className="hidden md:block text-left">
                <p className="header-profile-name-themed font-medium">{user?.name || 'User'}</p>
                <p className="header-profile-role-themed">{user?.role || 'User'}</p>
              </div>
              <svg className="h-4 w-4 text-slate-400 layout-transition-all" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Profile Dropdown Menu */}
            {isProfileOpen && (
              <div className="header-dropdown-themed">
                <button
                  onClick={handleProfileNavigation}
                  className="header-dropdown-item-themed"
                >
                  Profile Settings
                </button>
                <button
                  onClick={handleProfileNavigation}
                  className="header-dropdown-item-themed"
                >
                  Account Settings
                </button>
                <hr className="my-1 border-slate-500" />
                <button
                  onClick={handleSignOut}
                  className="header-dropdown-item-themed danger"
                >
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
