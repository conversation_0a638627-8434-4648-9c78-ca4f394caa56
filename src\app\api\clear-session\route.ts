// src/app/api/clear-session/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const response = NextResponse.json({ 
      success: true, 
      message: 'Session cleared successfully' 
    });

    // Clear the session cookie
    response.cookies.delete('session');
    
    return response;
  } catch (error) {
    console.error('Error clearing session:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to clear session' 
    }, { status: 500 });
  }
}
