// src/utils/helpers/transformers.ts
// Data transformation utilities

import type { 
  Student, 
  StudentWithRelations, 
  Class, 
  Section, 
  AcademicYear, 
  GuardianRelation 
} from '@/lib/supabase/types';

/**
 * Student data transformers
 */
export const studentTransformers = {
  /**
   * Transform student data for display
   */
  toDisplayFormat: (student: StudentWithRelations): {
    id: string;
    fullName: string;
    displayName: string;
    age: number;
    className: string;
    sectionName: string;
    academicYear: string;
    guardianInfo: string;
    rollNumber: string;
    isActive: boolean;
  } => {
    const fullName = `${student.first_name} ${student.last_name}`;
    const birthDate = new Date(student.date_of_birth);
    const age = new Date().getFullYear() - birthDate.getFullYear();
    
    return {
      id: student.id,
      fullName,
      displayName: fullName,
      age,
      className: student.class?.name || 'N/A',
      sectionName: student.section?.name || 'N/A',
      academicYear: student.academic_year?.year || 'N/A',
      guardianInfo: `${student.guardian_name} (${student.guardian_relation?.name || 'Guardian'})`,
      rollNumber: student.roll_number,
      isActive: student.is_active
    };
  },

  /**
   * Transform student for form editing
   */
  toFormData: (student: Student): {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    email: string;
    phoneNumber: string;
    address: string;
    guardianName: string;
    guardianRelationId: string;
    guardianPhone: string;
    guardianEmail: string;
    guardianAddress: string;
    emergencyContact: string;
    classId: string;
    sectionId: string;
    rollNumber: string;
    previousSchool: string;
    academicYearId: string;
  } => ({
    firstName: student.first_name,
    lastName: student.last_name,
    dateOfBirth: student.date_of_birth,
    gender: student.gender,
    email: student.email || '',
    phoneNumber: student.phone_number || '',
    address: student.address || '',
    guardianName: student.guardian_name,
    guardianRelationId: student.guardian_relation_id,
    guardianPhone: student.guardian_phone,
    guardianEmail: student.guardian_email || '',
    guardianAddress: student.guardian_address || '',
    emergencyContact: student.emergency_contact || '',
    classId: student.class_id,
    sectionId: student.section_id,
    rollNumber: student.roll_number,
    previousSchool: student.previous_school || '',
    academicYearId: student.academic_year_id
  }),

  /**
   * Transform form data to student insert format
   */
  fromFormData: (formData: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: 'male' | 'female' | 'other';
    email?: string;
    phoneNumber?: string;
    address?: string;
    guardianName: string;
    guardianRelationId: string;
    guardianPhone: string;
    guardianEmail?: string;
    guardianAddress?: string;
    emergencyContact?: string;
    classId: string;
    sectionId: string;
    rollNumber: string;
    previousSchool?: string;
    academicYearId: string;
  }) => ({
    first_name: formData.firstName.trim(),
    last_name: formData.lastName.trim(),
    date_of_birth: formData.dateOfBirth,
    gender: formData.gender,
    email: formData.email?.trim() || null,
    phone_number: formData.phoneNumber?.trim() || null,
    address: formData.address?.trim() || null,
    guardian_name: formData.guardianName.trim(),
    guardian_relation_id: formData.guardianRelationId,
    guardian_phone: formData.guardianPhone.trim(),
    guardian_email: formData.guardianEmail?.trim() || null,
    guardian_address: formData.guardianAddress?.trim() || null,
    emergency_contact: formData.emergencyContact?.trim() || null,
    class_id: formData.classId,
    section_id: formData.sectionId,
    roll_number: formData.rollNumber.toUpperCase().trim(),
    previous_school: formData.previousSchool?.trim() || null,
    academic_year_id: formData.academicYearId,
    is_active: true
  }),

  /**
   * Transform students for export
   */
  toExportFormat: (students: StudentWithRelations[]): Array<{
    'Roll Number': string;
    'First Name': string;
    'Last Name': string;
    'Date of Birth': string;
    'Gender': string;
    'Email': string;
    'Phone': string;
    'Address': string;
    'Class': string;
    'Section': string;
    'Academic Year': string;
    'Guardian Name': string;
    'Guardian Relation': string;
    'Guardian Phone': string;
    'Guardian Email': string;
    'Emergency Contact': string;
    'Previous School': string;
    'Status': string;
  }> => {
    return students.map(student => ({
      'Roll Number': student.roll_number,
      'First Name': student.first_name,
      'Last Name': student.last_name,
      'Date of Birth': student.date_of_birth,
      'Gender': student.gender,
      'Email': student.email || '',
      'Phone': student.phone_number || '',
      'Address': student.address || '',
      'Class': student.class?.name || '',
      'Section': student.section?.name || '',
      'Academic Year': student.academic_year?.year || '',
      'Guardian Name': student.guardian_name,
      'Guardian Relation': student.guardian_relation?.name || '',
      'Guardian Phone': student.guardian_phone,
      'Guardian Email': student.guardian_email || '',
      'Emergency Contact': student.emergency_contact || '',
      'Previous School': student.previous_school || '',
      'Status': student.is_active ? 'Active' : 'Inactive'
    }));
  }
};

/**
 * Master data transformers
 */
export const masterDataTransformers = {
  /**
   * Transform classes to dropdown options
   */
  classesToOptions: (classes: Class[]) => 
    classes.map(cls => ({
      value: cls.id,
      label: cls.name,
      description: cls.description
    })),

  /**
   * Transform sections to dropdown options
   */
  sectionsToOptions: (sections: Section[]) =>
    sections.map(section => ({
      value: section.id,
      label: section.name,
      description: section.description
    })),

  /**
   * Transform academic years to dropdown options
   */
  academicYearsToOptions: (academicYears: AcademicYear[]) =>
    academicYears.map(year => ({
      value: year.id,
      label: year.year,
      isCurrent: year.is_current,
      startDate: year.start_date,
      endDate: year.end_date
    })),

  /**
   * Transform guardian relations to dropdown options
   */
  guardianRelationsToOptions: (relations: GuardianRelation[]) =>
    relations.map(relation => ({
      value: relation.id,
      label: relation.name,
      description: relation.description
    }))
};

/**
 * API response transformers
 */
export const apiTransformers = {
  /**
   * Transform API error to user-friendly message
   */
  errorToMessage: (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.error?.message) return error.error.message;
    return 'An unexpected error occurred';
  },

  /**
   * Transform pagination data
   */
  paginationData: (data: {
    items: any[];
    total: number;
    page: number;
    limit: number;
  }) => ({
    items: data.items,
    pagination: {
      total: data.total,
      page: data.page,
      limit: data.limit,
      totalPages: Math.ceil(data.total / data.limit),
      hasNext: data.page * data.limit < data.total,
      hasPrev: data.page > 1
    }
  }),

  /**
   * Transform search results
   */
  searchResults: <T>(results: T[], query: string, totalCount: number) => ({
    results,
    query,
    count: results.length,
    totalCount,
    hasMore: results.length < totalCount
  })
};

/**
 * Date transformers
 */
export const dateTransformers = {
  /**
   * Transform date to various formats
   */
  toFormats: (date: string | Date) => {
    const d = new Date(date);
    return {
      iso: d.toISOString(),
      date: d.toISOString().split('T')[0],
      time: d.toTimeString().split(' ')[0],
      readable: d.toLocaleDateString(),
      timestamp: d.getTime(),
      relative: getRelativeTime(d)
    };
  },

  /**
   * Transform academic year dates
   */
  academicYearRange: (startYear: number) => ({
    startYear,
    endYear: startYear + 1,
    label: `${startYear}-${(startYear + 1).toString().slice(-2)}`,
    startDate: `${startYear}-04-01`,
    endDate: `${startYear + 1}-03-31`
  })
};

/**
 * File transformers
 */
export const fileTransformers = {
  /**
   * Transform file to upload format
   */
  toUploadFormat: (file: File, category: string) => ({
    file,
    name: file.name,
    size: file.size,
    type: file.type,
    category,
    id: generateFileId()
  }),

  /**
   * Transform file list to upload batch
   */
  toBatchUpload: (files: FileList | File[], category: string) => 
    Array.from(files).map(file => fileTransformers.toUploadFormat(file, category))
};

/**
 * Form transformers
 */
export const formTransformers = {
  /**
   * Transform form data to clean object
   */
  cleanFormData: (formData: Record<string, any>) => {
    const cleaned: Record<string, any> = {};
    
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (typeof value === 'string') {
          cleaned[key] = value.trim();
        } else {
          cleaned[key] = value;
        }
      }
    });
    
    return cleaned;
  },

  /**
   * Transform nested form data
   */
  flattenFormData: (data: Record<string, any>, prefix = ''): Record<string, any> => {
    const flattened: Record<string, any> = {};
    
    Object.entries(data).forEach(([key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        Object.assign(flattened, formTransformers.flattenFormData(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    });
    
    return flattened;
  }
};

/**
 * Helper functions
 */
function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  return date.toLocaleDateString();
}

function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
