// src/components/auth/auth-provider.tsx
'use client';

import { User } from '@/utils/auth';
import authStateManager from '@/utils/auth-state-manager';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error: string | null }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error: string | null }>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Use centralized auth state
  const [authState, setAuthState] = useState(authStateManager.getState());

  // Subscribe to auth state changes
  useEffect(() => {
    const unsubscribe = authStateManager.subscribe(setAuthState);
    return unsubscribe;
  }, []);

  const { user, loading, error } = authState;

  useEffect(() => {
    let isMounted = true;
    let subscription: any = null;
    let timeoutId: NodeJS.Timeout;

    // Force loading to false after maximum timeout to prevent infinite loading
    const forceLoadingComplete = () => {
      if (isMounted) {
        authStateManager.setState({ loading: false, error: null });
      }
    };

    const initializeAuth = async () => {
      // Prevent multiple concurrent initializations
      if (authStateManager.isInitializing()) {
        console.log('[AUTH_PROVIDER] Skipping initialization - already in progress');
        return;
      }

      console.log('AuthProvider: Starting auth initialization...');
      try {
        authStateManager.setInitializing(true);
        // Set longer timeout for session verification to prevent premature timeouts
        timeoutId = setTimeout(() => {
          console.log('AuthProvider: Force completing loading due to timeout');
          forceLoadingComplete();
        }, 10000);

        // Use enhanced auth check with retry logic and connection error handling
        const [{ checkAuthStateWithRetry }, { withRetry }] = await Promise.all([
          import('../../utils/auth'),
          import('../../utils/connection-handler')
        ]);

        console.log('AuthProvider: Checking auth state with retry...');
        const isAuthenticated = await withRetry(
          () => checkAuthStateWithRetry(2),
          { maxRetries: 2, baseDelay: 1000 }
        );
        console.log('AuthProvider: Auth state check result:', isAuthenticated);

        if (isAuthenticated && isMounted) {
          // Get the user data after successful auth check
          const { verifySession } = await import('@/lib/supabase/session');
          console.log('AuthProvider: Verifying session...');
          const sessionUser = await verifySession();
          console.log('AuthProvider: Session verification result:', !!sessionUser);

          if (sessionUser) {
            clearTimeout(timeoutId);
            console.log('AuthProvider: Setting authenticated user');
            authStateManager.setUser(sessionUser);

            // Start session heartbeat to prevent unexpected logouts
            const { startSessionHeartbeat } = await import('@/lib/session-heartbeat');
            startSessionHeartbeat();

            return;
          }
        }

        // If no authentication found after retries
        if (isMounted) {
          clearTimeout(timeoutId);
          console.log('AuthProvider: No authentication found, setting loading to false');
          authStateManager.setState({
            user: null,
            error: null,
            loading: false,
            initialized: true
          });
        }
      } catch (error) {
        // Handle connection errors gracefully
        try {
          const { isConnectionResetError } = await import('@/utils/connection-handler');
          if (isConnectionResetError(error)) {
            console.warn('Auth initialization failed due to connection reset, will retry on next mount:', error);
          } else {
            console.error('Auth initialization error:', error);
          }
        } catch {
          console.error('Auth initialization error:', error);
        }

        if (isMounted) {
          clearTimeout(timeoutId);
          // Don't immediately set user to null on error - preserve state
          authStateManager.setState({ error: null, loading: false });
        }
      }
    };

    // Set up auth state listener with error handling - but don't let it override session-based auth
    const setupAuthListener = async () => {
      try {
        const { onAuthStateChange } = await import('@/lib/supabase/auth');

        const { data } = await onAuthStateChange((supabaseUser) => {
          if (isMounted) {
            // Only update user state if we don't already have a valid session user
            // This prevents Supabase auth state changes from overriding session-based auth
            if ((!user || !user.isAuthenticated) && supabaseUser) {
              authStateManager.setUser(supabaseUser);
            } else if (loading) {
              authStateManager.setLoading(false);
            }
          }
        });
        subscription = data?.subscription;
      } catch (_error) {
        // Silently fail and ensure loading is false
        if (isMounted) {
          authStateManager.setLoading(false);
        }
      }
    };

    // Start initialization
    initializeAuth();
    setupAuthListener();

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription?.unsubscribe();
    };
  }, []); // Remove 'user' and 'loading' dependencies to prevent re-initialization

  const signIn = async (email: string, password: string) => {
    try {
      const { signIn: authSignIn } = await import('@/lib/supabase/auth');
      const result = await authSignIn(email, password);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('@/lib/supabase/session');
        await createSession(result.user);
        authStateManager.setUser(result.user);

        // Start session heartbeat
        const { startSessionHeartbeat } = await import('@/lib/session-heartbeat');
        startSessionHeartbeat();
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    try {
      const { signUp: authSignUp } = await import('@/lib/supabase/auth');
      const result = await authSignUp(email, password, userData);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('@/lib/supabase/session');
        await createSession(result.user);
        authStateManager.setUser(result.user);

        // Start session heartbeat
        const { startSessionHeartbeat } = await import('@/lib/session-heartbeat');
        startSessionHeartbeat();
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signOut = async () => {
    try {
      // Stop session heartbeat
      const { stopSessionHeartbeat } = await import('@/lib/session-heartbeat');
      stopSessionHeartbeat();

      const { signOut: authSignOut } = await import('@/lib/supabase/auth');
      const { deleteSession } = await import('@/lib/supabase/session');

      // Sign out from Supabase
      await authSignOut();

      // Delete secure session
      await deleteSession();

      authStateManager.reset();
    } catch (error) {
      console.error('Sign out error:', error);
      // Force sign out locally even if service fails
      const { stopSessionHeartbeat } = await import('../../lib/session-heartbeat');
      stopSessionHeartbeat();

      const { deleteSession } = await import('@/lib/supabase/session');
      await deleteSession();
      authStateManager.reset();
    }
  };

  const clearError = () => {
    authStateManager.setError('');
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
