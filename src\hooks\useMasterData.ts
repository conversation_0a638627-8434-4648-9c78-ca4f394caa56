'use client';

import { useState, useEffect, useCallback } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import type { 
  Class, 
  Section, 
  AcademicYear, 
  GuardianRelation 
} from '@/lib/supabase/types';

export interface MasterData {
  classes: Class[];
  sections: Section[];
  academicYears: AcademicYear[];
  guardianRelations: GuardianRelation[];
  currentAcademicYear: AcademicYear | null;
}

export interface UseMasterDataReturn {
  data: MasterData | null;
  loading: boolean;
  error: string | null;
  classes: Class[];
  sections: Section[];
  academicYears: AcademicYear[];
  guardianRelations: GuardianRelation[];
  currentAcademicYear: AcademicYear | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for fetching master data (classes, sections, academic years, guardian relations)
 * Provides centralized access to all dropdown/reference data used throughout the application
 */
export function useMasterData(): UseMasterDataReturn {
  const [data, setData] = useState<MasterData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMasterData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = await getSupabaseClient();

      // Fetch all master data in parallel
      const [classesResult, sectionsResult, academicYearsResult, guardianRelationsResult] = await Promise.all([
        supabase
          .from('classes')
          .select('*')
          .eq('is_active', true)
          .order('name'),
        
        supabase
          .from('sections')
          .select('*')
          .eq('is_active', true)
          .order('name'),
        
        supabase
          .from('academic_years')
          .select('*')
          .eq('is_active', true)
          .order('year', { ascending: false }),
        
        supabase
          .from('guardian_relations')
          .select('*')
          .eq('is_active', true)
          .order('name')
      ]);

      // Check for errors
      if (classesResult.error) {
        console.warn('Failed to fetch classes:', classesResult.error);
      }
      if (sectionsResult.error) {
        console.warn('Failed to fetch sections:', sectionsResult.error);
      }
      if (academicYearsResult.error) {
        console.warn('Failed to fetch academic years:', academicYearsResult.error);
      }
      if (guardianRelationsResult.error) {
        console.warn('Failed to fetch guardian relations:', guardianRelationsResult.error);
      }

      // Extract data (use empty arrays if fetch failed)
      const classes = classesResult.data || [];
      const sections = sectionsResult.data || [];
      const academicYears = academicYearsResult.data || [];
      const guardianRelations = guardianRelationsResult.data || [];

      // Find current academic year
      const currentAcademicYear = academicYears.find(year => year.is_current) || null;

      const masterData: MasterData = {
        classes,
        sections,
        academicYears,
        guardianRelations,
        currentAcademicYear
      };

      setData(masterData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch master data';
      setError(errorMessage);
      console.error('Master data fetch error:', err);
      
      // Set empty data on error
      setData({
        classes: [],
        sections: [],
        academicYears: [],
        guardianRelations: [],
        currentAcademicYear: null
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchMasterData();
  }, [fetchMasterData]);

  return {
    data,
    loading,
    error,
    classes: data?.classes || [],
    sections: data?.sections || [],
    academicYears: data?.academicYears || [],
    guardianRelations: data?.guardianRelations || [],
    currentAcademicYear: data?.currentAcademicYear || null,
    refetch: fetchMasterData
  };
}
