'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import AuthForm from '../../../components/auth/auth-form';

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');
  const router = useRouter();

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleReturnHome = () => {
    router.push('/product');
  };

  return (
    <div className="min-h-screen bg-slate-900/60 flex items-center justify-center backdrop-blur-sm p-4">
      <div
        className="bg-white rounded-2xl shadow-2xl border border-slate-200/50 w-full max-w-4xl mx-auto overflow-hidden min-h-[600px]"
        style={{
          maxHeight: 'calc(100vh - 2rem)',
          fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif"
        }}
      >
        {/* Return Home Button */}
        <button
          onClick={handleReturnHome}
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg p-1.5 transition-all duration-200 z-20"
          aria-label="Return to home page"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Two-Panel Layout */}
        <div className="flex flex-col lg:flex-row min-h-full">
          {/* Left Panel - Authentication Form */}
          <div className="w-full lg:w-1/2 bg-white flex flex-col order-2 lg:order-1">
            {/* Form Header */}
            <div className="bg-gradient-to-r from-slate-50 to-slate-100/50 border-b border-slate-200/60 px-6 pt-6 pb-4">
              <div className="text-center">
                {/* Logo and Brand */}
                <div className="flex items-center justify-center mb-4 gap-2">
                  <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg flex items-center justify-center shadow-md w-8 h-8">
                    <svg className="text-white w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                    </svg>
                  </div>
                  <span className="text-xl font-bold text-slate-900">EduPro</span>
                </div>

                {/* Tab Navigation */}
                <div className="flex bg-slate-100/80 rounded-lg p-1 max-w-xs mx-auto">
                  <button
                    onClick={() => handleToggle('signup')}
                    className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 ${
                      activeTab === 'signup'
                        ? 'bg-white text-indigo-600 shadow-sm'
                        : 'text-slate-600 hover:text-slate-900'
                    }`}
                    aria-pressed={activeTab === 'signup'}
                    aria-label="Switch to sign up form"
                  >
                    Sign Up
                  </button>
                  <button
                    onClick={() => handleToggle('login')}
                    className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 ${
                      activeTab === 'login'
                        ? 'bg-white text-indigo-600 shadow-sm'
                        : 'text-slate-600 hover:text-slate-900'
                    }`}
                    aria-pressed={activeTab === 'login'}
                    aria-label="Switch to sign in form"
                  >
                    Sign In
                  </button>
                </div>
              </div>
            </div>

            {/* Form Body */}
            <div className="bg-white px-6 py-5 flex-1 overflow-y-auto">
              <div className="text-center mb-5">
                <h3 className="text-lg font-semibold text-slate-900 mb-1">
                  {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
                </h3>
                <p className="text-slate-600 text-sm">
                  {activeTab === 'signup'
                    ? 'Join thousands of educators worldwide'
                    : 'Sign in to continue your journey'
                  }
                </p>
              </div>
              <AuthForm formType={activeTab} onToggleForm={handleToggle} />
            </div>
          </div>

          {/* Right Panel - Marketing Content */}
          <div className="w-full lg:w-1/2 bg-gradient-to-br from-indigo-600 via-indigo-700 to-indigo-800 relative overflow-hidden order-1 lg:order-2 min-h-[200px] lg:min-h-full">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <defs>
                  <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
                  </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
              </svg>
            </div>

            {/* Subtle Overlay for Depth */}
            <div className="absolute inset-0 bg-gradient-to-t from-indigo-800/20 via-transparent to-indigo-500/10"></div>

            {/* Educational Illustration */}
            <div className="absolute top-4 right-4 lg:top-8 lg:right-8 opacity-20">
              <svg className="w-16 h-16 lg:w-24 lg:h-24" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
              </svg>
            </div>

            {/* Floating Elements */}
            <div className="absolute top-1/4 left-4 lg:left-8 opacity-15">
              <div className="w-3 h-3 lg:w-4 lg:h-4 bg-white rounded-full animate-pulse"></div>
            </div>
            <div className="absolute bottom-1/3 right-1/4 opacity-15">
              <div className="w-2 h-2 lg:w-3 lg:h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
            </div>
            <div className="absolute top-1/2 left-1/3 opacity-15">
              <div className="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-white rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
            </div>

            {/* Content */}
            <div className="relative z-10 p-6 lg:p-8 h-full flex flex-col justify-center text-white">
              {/* Main Headline */}
              <div className="mb-6 lg:mb-8">
                <h2 className="text-white text-2xl lg:text-3xl font-bold mb-3 lg:mb-4 leading-tight">
                  Transform Your Educational Institution
                </h2>
                <p className="text-indigo-50 text-base lg:text-lg leading-relaxed">
                  Streamline student management, enhance learning outcomes, and empower educators with our comprehensive education management platform.
                </p>
              </div>

              {/* Features List */}
              <div className="mb-6 lg:mb-8 space-y-3 lg:space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 lg:w-6 lg:h-6 bg-indigo-400/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-2.5 h-2.5 lg:w-3 lg:h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-indigo-50 text-sm lg:text-base">Complete student enrollment & management</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 lg:w-6 lg:h-6 bg-indigo-400/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-2.5 h-2.5 lg:w-3 lg:h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-indigo-50 text-sm lg:text-base">Real-time attendance tracking & analytics</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 lg:w-6 lg:h-6 bg-indigo-400/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-2.5 h-2.5 lg:w-3 lg:h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-indigo-50 text-sm lg:text-base">Automated fee collection & financial reports</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 lg:w-6 lg:h-6 bg-indigo-400/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-2.5 h-2.5 lg:w-3 lg:h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-indigo-50 text-sm lg:text-base">Comprehensive staff & academic management</span>
                </div>
              </div>

              {/* Testimonial */}
              <div className="hidden lg:block">
                <div className="bg-white/8 backdrop-blur-sm rounded-lg p-4 lg:p-6 border border-white/15">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-indigo-500/30 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-white/95 text-sm leading-relaxed mb-3">
                        "The automated workflows have saved us countless hours. Our administrative staff can now focus on what matters most - supporting our students."
                      </p>
                      <div>
                        <p className="text-white font-medium text-sm">Michael Chen</p>
                        <p className="text-indigo-200 text-xs">Principal, Riverside Academy</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
