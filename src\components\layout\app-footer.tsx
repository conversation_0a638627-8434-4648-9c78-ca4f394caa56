// src/components/layout/app-footer.tsx
'use client';

const AppFooter = () => {
  return (
    <footer className="footer-themed">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <p className="footer-text-primary-themed">
            © 2024 EduPro. All rights reserved.
          </p>
        </div>

        <div className="flex items-center space-x-6">
          <a href="#" className="footer-link-themed">
            Help
          </a>
          <a href="#" className="footer-link-themed">
            Privacy
          </a>
          <a href="#" className="footer-link-themed">
            Terms
          </a>
          <div className="footer-text-secondary-themed">
            Version 2.0.0
          </div>
        </div>
      </div>
    </footer>
  );
};

export default AppFooter;
