// src/utils/constants/api.ts
// API endpoints and configuration constants

/**
 * API base URLs
 */
export const API_BASE_URLS = {
  SUPABASE: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  LOCAL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
} as const;

/**
 * API routes for Next.js API handlers
 */
export const API_ROUTES = {
  AUTH: {
    SIGN_IN: '/api/auth/signin',
    SIGN_UP: '/api/auth/signup',
    SIGN_OUT: '/api/auth/signout',
    VERIFY: '/api/auth/verify',
    REFRESH: '/api/auth/refresh',
  },
  STUDENTS: {
    LIST: '/api/students',
    CREATE: '/api/students',
    UPDATE: (id: string) => `/api/students/${id}`,
    DELETE: (id: string) => `/api/students/${id}`,
    BULK: '/api/students/bulk',
  },
  MASTER_DATA: {
    CLASSES: '/api/master-data/classes',
    SECTIONS: '/api/master-data/sections',
    ACADEMIC_YEARS: '/api/master-data/academic-years',
    GUARDIAN_RELATIONS: '/api/master-data/guardian-relations',
    ALL: '/api/master-data',
  },
  UPLOADS: {
    DOCUMENTS: '/api/uploads/documents',
    IMAGES: '/api/uploads/images',
  },
} as const;

/**
 * Application routes for navigation
 */
export const APP_ROUTES = {
  // Public routes
  HOME: '/',
  PRODUCT: '/product',
  RESOURCES: '/resources',
  AUTH: '/auth',
  
  // Protected routes
  DASHBOARD: '/dashboard',
  STUDENT_MANAGEMENT: '/student-management',
  STAFF_MANAGEMENT: '/staff-management',
  ACADEMIC_MANAGEMENT: '/academic-management',
  ATTENDANCE_MANAGEMENT: '/attendance-management',
  FEE_MANAGEMENT: '/fee-management',
  REPORTS: '/reports',
  SETTINGS: '/settings',
  
  // Error pages
  NOT_FOUND: '/404',
  ERROR: '/error',
  UNAUTHORIZED: '/unauthorized',
} as const;

/**
 * HTTP status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

/**
 * HTTP methods
 */
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
} as const;

/**
 * Content types
 */
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
} as const;

/**
 * Request timeout configurations
 */
export const TIMEOUTS = {
  DEFAULT: 10000, // 10 seconds
  UPLOAD: 30000, // 30 seconds
  LONG_RUNNING: 60000, // 1 minute
  AUTH: 5000, // 5 seconds
} as const;

/**
 * Rate limiting configurations
 */
export const RATE_LIMITS = {
  AUTH: {
    WINDOW_MS: 15 * 60 * 1000, // 15 minutes
    MAX_ATTEMPTS: 5,
  },
  API: {
    WINDOW_MS: 60 * 1000, // 1 minute
    MAX_REQUESTS: 100,
  },
  UPLOADS: {
    WINDOW_MS: 60 * 1000, // 1 minute
    MAX_UPLOADS: 10,
  },
} as const;

/**
 * File upload configurations
 */
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
  ],
  MAX_FILES_PER_UPLOAD: 5,
} as const;

/**
 * Pagination defaults
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

/**
 * Cache configurations
 */
export const CACHE_CONFIG = {
  MASTER_DATA: {
    TTL: 5 * 60 * 1000, // 5 minutes
    KEY_PREFIX: 'master_data_',
  },
  STUDENTS: {
    TTL: 2 * 60 * 1000, // 2 minutes
    KEY_PREFIX: 'students_',
  },
  USER_SESSION: {
    TTL: 30 * 60 * 1000, // 30 minutes
    KEY_PREFIX: 'session_',
  },
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  TIMEOUT: 'Request timed out. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  SERVER_ERROR: 'An unexpected error occurred. Please try again later.',
  RATE_LIMITED: 'Too many requests. Please wait and try again.',
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  CREATED: 'Successfully created.',
  UPDATED: 'Successfully updated.',
  DELETED: 'Successfully deleted.',
  UPLOADED: 'File uploaded successfully.',
  SIGNED_IN: 'Successfully signed in.',
  SIGNED_OUT: 'Successfully signed out.',
} as const;

/**
 * Helper function to build API URL
 */
export function buildApiUrl(endpoint: string, baseUrl: string = API_BASE_URLS.LOCAL): string {
  return `${baseUrl.replace(/\/$/, '')}${endpoint}`;
}

/**
 * Helper function to build query string
 */
export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}

/**
 * Type definitions
 */
export type ApiRoute = typeof API_ROUTES[keyof typeof API_ROUTES];
export type AppRoute = typeof APP_ROUTES[keyof typeof APP_ROUTES];
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];
export type HttpMethod = typeof HTTP_METHODS[keyof typeof HTTP_METHODS];
export type ContentType = typeof CONTENT_TYPES[keyof typeof CONTENT_TYPES];
