// src/lib/supabase/dal.ts
'use server';

import { User } from '@/utils/auth';
import { cache } from 'react';
import { verifySession } from './session';

/**
 * Data Access Layer (DAL) for authentication
 * Provides cached session verification to avoid repeated checks
 */

/**
 * Get current authenticated user with caching
 * This function is cached to avoid repeated session verification calls
 */
export const getUser = cache(async (): Promise<User | null> => {
  const session = await verifySession();
  return session;
});

/**
 * Verify if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getUser();
  return user !== null;
}

/**
 * Verify if user has specific role
 */
export async function hasRole(role: string): Promise<boolean> {
  const user = await getUser();
  return user?.role === role;
}

/**
 * Verify if user has any of the specified roles
 */
export async function hasAnyRole(roles: string[]): Promise<boolean> {
  const user = await getUser();
  return user ? roles.includes(user.role) : false;
}

/**
 * Get user or throw error if not authenticated
 * Use this in Server Actions or API routes where authentication is required
 */
export async function requireAuth(): Promise<User> {
  const user = await getUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

/**
 * Require specific role or throw error
 */
export async function requireRole(role: string): Promise<User> {
  const user = await requireAuth();
  
  if (user.role !== role) {
    throw new Error(`Role '${role}' required`);
  }
  
  return user;
}

/**
 * Require any of the specified roles or throw error
 */
export async function requireAnyRole(roles: string[]): Promise<User> {
  const user = await requireAuth();
  
  if (!roles.includes(user.role)) {
    throw new Error(`One of roles [${roles.join(', ')}] required`);
  }
  
  return user;
}
