'use client';

import { useState, useEffect, useCallback } from 'react';
import { verifySession } from '@/lib/supabase/session';
import { signIn as supabaseSignIn, signOut as supabaseSignOut, signUp as supabaseSignUp } from '@/lib/supabase/auth';
import { createSession, deleteSession } from '@/lib/supabase/session';
import type { User } from '@/utils/auth';

export interface UseAuthReturn {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

/**
 * Enhanced authentication hook with session management
 * Provides centralized auth state and operations
 */
export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshAuth = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const sessionUser = await verifySession();
      setUser(sessionUser);
    } catch (err) {
      console.error('Auth refresh error:', err);
      setError(err instanceof Error ? err.message : 'Authentication check failed');
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setError(null);
      setLoading(true);

      const result = await supabaseSignIn(email, password);
      
      if (result.success && result.user) {
        // Create secure session
        await createSession(result.user);
        setUser(result.user);
        
        return { success: true };
      } else {
        setError(result.error || 'Sign in failed');
        return { success: false, error: result.error || 'Sign in failed' };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sign in failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  const signUp = useCallback(async (email: string, password: string, userData: { name: string; role: string }) => {
    try {
      setError(null);
      setLoading(true);

      const result = await supabaseSignUp(email, password, userData);
      
      if (result.success && result.user) {
        // Create secure session
        await createSession(result.user);
        setUser(result.user);
        
        return { success: true };
      } else {
        setError(result.error || 'Sign up failed');
        return { success: false, error: result.error || 'Sign up failed' };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sign up failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      setError(null);
      
      // Sign out from Supabase
      await supabaseSignOut();
      
      // Delete secure session
      await deleteSession();
      
      setUser(null);
    } catch (err) {
      console.error('Sign out error:', err);
      // Force sign out locally even if service fails
      await deleteSession();
      setUser(null);
    }
  }, []);

  // Initial auth check
  useEffect(() => {
    refreshAuth();
  }, [refreshAuth]);

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signOut,
    refreshAuth
  };
}

/**
 * Hook for checking if user has specific role
 */
export function useRole(requiredRole: string) {
  const { user, loading } = useAuth();
  
  return {
    hasRole: user?.role === requiredRole,
    loading,
    user
  };
}

/**
 * Hook for checking if user has any of the specified roles
 */
export function useRoles(requiredRoles: string[]) {
  const { user, loading } = useAuth();
  
  return {
    hasAnyRole: user ? requiredRoles.includes(user.role) : false,
    loading,
    user
  };
}

/**
 * Hook for requiring authentication
 * Throws error if user is not authenticated
 */
export function useRequireAuth() {
  const { user, loading, error } = useAuth();
  
  useEffect(() => {
    if (!loading && !user) {
      throw new Error('Authentication required');
    }
  }, [user, loading]);
  
  return { user, loading, error };
}
