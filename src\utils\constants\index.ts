// src/utils/constants/index.ts
// Centralized exports for all constants

// Database constants
export * from './database';

// API constants
export * from './api';

// Validation constants
export * from './validation';

// UI constants
export * from './ui';

// Re-export commonly used constants with aliases for convenience
export {
  DATABASE_TABLES as TABLES,
  DATABASE_COLUMNS as COLUMNS,
  DATABASE_SCHEMAS as SCHEMAS,
  STORAGE_BUCKETS as BUCKETS
} from './database';

export {
  API_ROUTES as ROUTES,
  APP_ROUTES as PAGES,
  HTTP_STATUS as STATUS,
  HTTP_METHODS as METHODS
} from './api';

export {
  VALIDATION_PATTERNS as PATTERNS,
  VALIDATION_MESSAGES as MESSAGES,
  FIELD_LENGTHS as LENGTHS
} from './validation';

export {
  COLORS,
  TYPOGRAPHY,
  SPACING,
  COMPONENT_SIZES as SIZES
} from './ui';
