// src/app/(dashboard)/layout.tsx
import { Metadata } from 'next';
import { AppLayout } from '../../components/layout';

// This layout wraps all dashboard routes with AppLayout
// Provides consistent sidebar, header, and footer for all management pages

export const metadata: Metadata = {
  title: 'Dashboard - EduPro',
  description: 'EduPro management dashboard with comprehensive school administration tools',
  keywords: 'dashboard, school management, education administration',
  authors: [{ name: 'EduPro Team' }],
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}
