// src/utils/auth.ts
'use client';

export interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export const getAuthState = async (): Promise<User | null> => {
  // Check session-based authentication
  try {
    const { verifySession } = await import('./supabase/session');
    return await verifySession();
  } catch (error) {
    console.error('Failed to get auth state:', error);
    return null;
  }
};

export const logout = async () => {
  if (typeof window !== 'undefined') {
    try {
      // Sign out from Supabase
      const { signOut } = await import('./supabase/auth');
      await signOut();

      // Delete session
      const { deleteSession } = await import('./supabase/session');
      await deleteSession();

      // Redirect to product page after logout
      window.location.href = '/product';
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if logout fails
      window.location.href = '/product';
    }
  }
};

export const requireAuth = async () => {
  if (typeof window !== 'undefined') {
    // Check session-based authentication first
    try {
      const { verifySession } = await import('./supabase/session');
      const sessionUser = await verifySession();
      if (sessionUser) {
        return true;
      }
    } catch (error) {
      console.error('Session verification failed:', error);
    }

    // Fallback to Supabase auth check
    try {
      const { getAuthState: getSupabaseAuthState } = await import('./supabase/auth');
      const supabaseUser = await getSupabaseAuthState();
      if (supabaseUser) {
        return true;
      }
    } catch (error) {
      console.error('Supabase auth check failed:', error);
    }

    // No valid authentication found
    console.log('No valid authentication found, redirecting to auth page');
    window.location.href = '/auth';
    return false;
  }
  return false;
};

export const getUser = async (): Promise<User | null> => {
  return await getAuthState();
};

/**
 * Enhanced auth state checker with retry logic
 */
export async function checkAuthStateWithRetry(maxRetries: number = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const { verifySession } = await import('./supabase/session');
      const sessionUser = await verifySession();

      if (sessionUser) {
        return true;
      }

      // If no session, check Supabase as fallback
      const { getCurrentUser } = await import('./supabase/auth');
      const currentUser = await getCurrentUser();

      if (currentUser) {
        // Create session for existing Supabase user
        const { createSession } = await import('./supabase/session');
        await createSession(currentUser);
        return true;
      }

      // If this is not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    } catch (error) {
      console.error(`Auth check attempt ${attempt} failed:`, error);

      // If this is not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  return false;
}
