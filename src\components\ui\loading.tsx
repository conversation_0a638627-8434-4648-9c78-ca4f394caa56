'use client';

import { useEffect, useState } from 'react';

interface LoadingProps {
  message?: string;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'branded' | 'modern';
  fullScreen?: boolean;
  overlay?: boolean;
}

export const Loading = ({ 
  message = "Loading...", 
  showProgress = true, 
  size = 'md',
  variant = 'modern',
  fullScreen = true,
  overlay = false
}: LoadingProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingPhase, setLoadingPhase] = useState(0);
  const [dots, setDots] = useState('');

  const loadingPhases = [
    { message: 'Initializing EduPro', color: 'from-blue-500 to-cyan-500', bgColor: 'bg-blue-50' },
    { message: 'Loading workspace', color: 'from-cyan-500 to-emerald-500', bgColor: 'bg-cyan-50' },
    { message: 'Setting up dashboard', color: 'from-emerald-500 to-teal-500', bgColor: 'bg-emerald-50' },
    { message: 'Preparing interface', color: 'from-teal-500 to-blue-500', bgColor: 'bg-teal-50' },
    { message: 'Almost ready', color: 'from-blue-500 to-violet-500', bgColor: 'bg-violet-50' }
  ];

  useEffect(() => {
    // Smooth progress animation
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const increment = Math.random() * 6 + 3;
        const newProgress = Math.min(prev + increment, 95);
        
        // Update phase based on progress
        const phaseIndex = Math.min(
          Math.floor((newProgress / 100) * loadingPhases.length), 
          loadingPhases.length - 1
        );
        setLoadingPhase(phaseIndex);
        
        return newProgress;
      });
    }, 200);

    // Animated dots
    const dotsInterval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 600);

    return () => {
      clearInterval(progressInterval);
      clearInterval(dotsInterval);
    };
  }, []);

  // Modern variant (new default)
  if (variant === 'modern') {
    const containerClass = fullScreen 
      ? "fixed inset-0 z-[9999]" 
      : "flex items-center justify-center min-h-[500px]";
    
    const bgClass = overlay 
      ? "bg-white/90 backdrop-blur-lg" 
      : "bg-gradient-to-br from-slate-50 via-white to-blue-50/40";

    return (
      <div className={`${containerClass} ${bgClass} flex items-center justify-center`}>
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-blue-400/10 to-cyan-400/10 rounded-full animate-float"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-gradient-to-r from-emerald-400/10 to-teal-400/10 rounded-full animate-float-delayed"></div>
          <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-gradient-to-r from-violet-400/10 to-purple-400/10 rounded-full animate-float-reverse"></div>
        </div>

        {/* Main loading container */}
        <div className="relative max-w-md mx-auto px-6">
          {/* Glassmorphism card */}
          <div className={`relative bg-white/60 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/5 border border-white/30 p-10 transition-all duration-500 ${loadingPhases[loadingPhase]?.bgColor}`}>
            
            {/* Logo and branding */}
            <div className="text-center mb-10">
              <div className="relative inline-block mb-6">
                {/* Animated logo container */}
                <div className="relative w-24 h-24 mx-auto">
                  {/* Outer rotating rings */}
                  <div className="absolute inset-0 rounded-full border-[3px] border-transparent border-t-blue-500 border-r-cyan-400 animate-spin"></div>
                  <div className="absolute inset-1 rounded-full border-2 border-transparent border-b-emerald-500 border-l-teal-400 animate-spin-reverse-slow"></div>
                  
                  {/* Logo center with dynamic gradient */}
                  <div className={`absolute inset-3 bg-gradient-to-br ${loadingPhases[loadingPhase]?.color} rounded-full flex items-center justify-center shadow-xl transition-all duration-500`}>
                    <span className="text-white font-bold text-2xl">EP</span>
                  </div>
                  
                  {/* Floating particles */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping"></div>
                  <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-emerald-400 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                  <div className="absolute top-3 -right-2 w-1.5 h-1.5 bg-violet-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                </div>
                
                {/* Brand name with animated gradient */}
                <h1 className={`text-4xl font-bold bg-gradient-to-r ${loadingPhases[loadingPhase]?.color} bg-clip-text text-transparent transition-all duration-500 mb-2`}>
                  EduPro
                </h1>
                <p className="text-sm text-slate-500 font-medium tracking-wide">
                  Education Management System
                </p>
              </div>
            </div>

            {/* Progress section */}
            {showProgress && (
              <div className="space-y-8">
                {/* Status indicator */}
                <div className="text-center">
                  <div className="inline-flex items-center space-x-3 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 mb-6 shadow-lg border border-white/40">
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${loadingPhases[loadingPhase]?.color} animate-pulse shadow-lg`}></div>
                    <span className="text-sm font-semibold text-slate-700">
                      {loadingPhases[loadingPhase]?.message}{dots}
                    </span>
                  </div>
                </div>

                {/* Enhanced progress bar */}
                <div className="space-y-4">
                  <div className="flex justify-between items-center text-sm">
                    <span className="font-semibold text-slate-600">Loading Progress</span>
                    <span className="font-bold text-slate-800 text-lg">{Math.round(progress)}%</span>
                  </div>
                  
                  <div className="relative">
                    {/* Progress track with glass effect */}
                    <div className="w-full h-4 bg-white/40 backdrop-blur-sm rounded-full overflow-hidden shadow-inner border border-white/30">
                      {/* Progress fill with animated gradient */}
                      <div 
                        className={`h-full bg-gradient-to-r ${loadingPhases[loadingPhase]?.color} transition-all duration-700 ease-out rounded-full relative overflow-hidden`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      >
                        {/* Shimmer animation */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent animate-shimmer rounded-full"></div>
                        {/* Inner glow */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-full"></div>
                      </div>
                    </div>
                    
                    {/* Progress indicator */}
                    <div 
                      className="absolute top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white rounded-full shadow-xl border-3 border-blue-400 transition-all duration-700 z-10"
                      style={{ left: `${Math.min(progress, 100)}%`, marginLeft: '-10px' }}
                    >
                      <div className="absolute inset-1 bg-blue-400 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Progress stats */}
                <div className="grid grid-cols-3 gap-6 pt-6 border-t border-white/30">
                  <div className="text-center">
                    <div className="text-xs text-slate-400 uppercase tracking-wider mb-2">Phase</div>
                    <div className="text-lg font-bold text-slate-700">{loadingPhase + 1}/5</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-slate-400 uppercase tracking-wider mb-2">Status</div>
                    <div className="text-lg font-bold text-emerald-600">Active</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-slate-400 uppercase tracking-wider mb-2">ETA</div>
                    <div className="text-lg font-bold text-slate-700">{Math.max(1, Math.ceil((100 - progress) / 8))}s</div>
                  </div>
                </div>
              </div>
            )}

            {/* Phase indicators */}
            <div className="flex justify-center mt-8 space-x-3">
              {loadingPhases.map((_, i) => (
                <div
                  key={i}
                  className={`w-3 h-3 rounded-full transition-all duration-500 ${
                    i <= loadingPhase 
                      ? `bg-gradient-to-r ${loadingPhases[i].color} scale-110 shadow-lg` 
                      : 'bg-slate-300/60 scale-90'
                  }`}
                ></div>
              ))}
            </div>

            {/* Inspirational quote */}
            <div className="text-center mt-10 pt-8 border-t border-white/30">
              <div className="flex items-center justify-center space-x-4 mb-3">
                <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-slate-300"></div>
                <svg className="w-6 h-6 text-slate-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                </svg>
                <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-slate-300"></div>
              </div>
              <p className="text-sm text-slate-500 italic font-medium leading-relaxed max-w-xs mx-auto">
                "Preparing your educational workspace with precision and care"
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Branded variant (enhanced)
  if (variant === 'branded') {
    const containerClasses = fullScreen 
      ? "fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex items-center justify-center z-50"
      : "flex items-center justify-center p-8";

    return (
      <div className={containerClasses}>
        <div className="text-center space-y-8 max-w-md mx-auto px-6">
          {/* Enhanced logo section */}
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className={`w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-emerald-500/40 relative overflow-hidden`}>
                <span className="text-white font-bold text-2xl z-10">EP</span>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-2xl animate-pulse opacity-70"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-2xl animate-spin-slow"></div>
              </div>
              <div className="absolute inset-0 w-20 h-20 border-3 border-transparent border-t-emerald-500 border-r-emerald-400 rounded-2xl animate-spin"></div>
            </div>
            
            <div className="space-y-3">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-emerald-700 bg-clip-text text-transparent animate-gradient">
                EduPro
              </h1>
              <p className="text-sm text-gray-500 font-medium tracking-wide">Education Management System</p>
            </div>
          </div>

          {showProgress && (
            <div className="space-y-6 w-full">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Loading EduPro...</span>
                  <span className="text-sm text-emerald-600 font-semibold">{Math.round(progress)}%</span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden shadow-inner relative">
                  <div 
                    className="h-full bg-gradient-to-r from-emerald-500 via-teal-500 to-emerald-600 transition-all duration-500 ease-out rounded-full shadow-lg relative overflow-hidden"
                    style={{ width: `${Math.min(progress, 100)}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
                    {progress > 30 && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-white/90 tracking-wider">EDUPRO</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-center space-x-2">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce shadow-lg"
                    style={{ 
                      animationDelay: `${i * 0.15}s`,
                      animationDuration: '1s'
                    }}
                  ></div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Minimal variant
  if (variant === 'minimal') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center space-x-3">
          <div className="relative w-6 h-6">
            <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-blue-500 animate-spin"></div>
          </div>
          <span className="text-sm font-medium text-slate-600">{message}</span>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className="flex flex-col items-center justify-center min-h-[300px] space-y-4">
      <div className="relative w-12 h-12">
        <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 border-r-blue-300 animate-spin"></div>
      </div>
      <p className="text-slate-600 font-medium">{message}</p>
    </div>
  );
};

export default Loading;
