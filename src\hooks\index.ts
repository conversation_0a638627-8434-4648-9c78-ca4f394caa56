// src/hooks/index.ts
// Centralized exports for all custom hooks

// Authentication hooks
export { useAuth, useRole, useRoles, useRequireAuth } from './useAuth';

// Data fetching hooks
export { useMasterData } from './useMasterData';
export { useStudents } from './useStudents';

// Real-time subscription hooks
export { 
  useRealtimeSubscription, 
  useStudentsRealtime, 
  useMasterDataRealtime 
} from './useRealtimeSubscription';

// Type exports for convenience
export type { UseAuthReturn } from './useAuth';
export type { UseMasterDataReturn, MasterData } from './useMasterData';
export type { UseStudentsReturn, UseStudentsFilters } from './useStudents';
export type { 
  UseRealtimeSubscriptionReturn, 
  UseRealtimeSubscriptionOptions,
  RealtimePayload,
  RealtimeEvent 
} from './useRealtimeSubscription';
