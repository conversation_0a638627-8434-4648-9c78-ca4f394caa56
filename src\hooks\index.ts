// src/hooks/index.ts
// Centralized exports for all custom hooks

// Authentication hooks
export { useAuth, useRequireAuth, useRole, useRoles } from './useAuth';
export { useAutoSessionRefresh, useSession, useSessionMonitor } from './useSession';

// Data fetching hooks
export { useMasterData } from './useMasterData';
export { useStudents } from './useStudents';

// Real-time subscription hooks
export {
    useMasterDataRealtime, useRealtimeSubscription,
    useStudentsRealtime
} from './useRealtimeSubscription';

// Type exports for convenience
export type { UseAuthReturn } from './useAuth';
export type { MasterData, UseMasterDataReturn } from './useMasterData';
export type {
    RealtimeEvent, RealtimePayload, UseRealtimeSubscriptionOptions, UseRealtimeSubscriptionReturn
} from './useRealtimeSubscription';
export type { UseSessionReturn } from './useSession';
export type { UseStudentsFilters, UseStudentsReturn } from './useStudents';

