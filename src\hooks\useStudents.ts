'use client';

import { useState, useEffect, useCallback } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import type { 
  Student, 
  StudentInsert, 
  StudentUpdate, 
  StudentWithRelations 
} from '@/lib/supabase/types';

export interface UseStudentsFilters {
  classId?: string;
  sectionId?: string;
  academicYearId?: string;
  isActive?: boolean;
  searchTerm?: string;
}

export interface UseStudentsReturn {
  students: StudentWithRelations[];
  loading: boolean;
  error: string | null;
  createStudent: (data: StudentInsert) => Promise<Student | null>;
  updateStudent: (id: string, data: StudentUpdate) => Promise<Student | null>;
  deleteStudent: (id: string) => Promise<boolean>;
  refetch: () => Promise<void>;
  setFilters: (filters: UseStudentsFilters) => void;
  filters: UseStudentsFilters;
}

/**
 * Custom hook for managing students data
 * Provides CRUD operations and filtering capabilities for student management
 */
export function useStudents(initialFilters: UseStudentsFilters = {}): UseStudentsReturn {
  const [students, setStudents] = useState<StudentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<UseStudentsFilters>(initialFilters);

  const fetchStudents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = await getSupabaseClient();

      // Build query with joins for related data
      let query = supabase
        .from('students')
        .select(`
          *,
          class:classes(id, name),
          section:sections(id, name),
          academic_year:academic_years(id, year),
          guardian_relation:guardian_relations(id, name),
          profile:profiles(id, full_name, email)
        `);

      // Apply filters
      if (filters.classId) {
        query = query.eq('class_id', filters.classId);
      }
      if (filters.sectionId) {
        query = query.eq('section_id', filters.sectionId);
      }
      if (filters.academicYearId) {
        query = query.eq('academic_year_id', filters.academicYearId);
      }
      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }
      if (filters.searchTerm) {
        query = query.or(`first_name.ilike.%${filters.searchTerm}%,last_name.ilike.%${filters.searchTerm}%,roll_number.ilike.%${filters.searchTerm}%`);
      }

      // Order by name
      query = query.order('first_name').order('last_name');

      const { data, error: fetchError } = await query;

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      setStudents(data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch students';
      setError(errorMessage);
      console.error('Students fetch error:', err);
      setStudents([]);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const createStudent = useCallback(async (data: StudentInsert): Promise<Student | null> => {
    try {
      setError(null);
      const supabase = await getSupabaseClient();

      const { data: newStudent, error: createError } = await supabase
        .from('students')
        .insert(data)
        .select()
        .single();

      if (createError) {
        throw new Error(createError.message);
      }

      // Refresh the students list
      await fetchStudents();

      return newStudent;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create student';
      setError(errorMessage);
      console.error('Student creation error:', err);
      return null;
    }
  }, [fetchStudents]);

  const updateStudent = useCallback(async (id: string, data: StudentUpdate): Promise<Student | null> => {
    try {
      setError(null);
      const supabase = await getSupabaseClient();

      const { data: updatedStudent, error: updateError } = await supabase
        .from('students')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (updateError) {
        throw new Error(updateError.message);
      }

      // Refresh the students list
      await fetchStudents();

      return updatedStudent;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update student';
      setError(errorMessage);
      console.error('Student update error:', err);
      return null;
    }
  }, [fetchStudents]);

  const deleteStudent = useCallback(async (id: string): Promise<boolean> => {
    try {
      setError(null);
      const supabase = await getSupabaseClient();

      // Soft delete by setting is_active to false
      const { error: deleteError } = await supabase
        .from('students')
        .update({ is_active: false })
        .eq('id', id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Refresh the students list
      await fetchStudents();

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete student';
      setError(errorMessage);
      console.error('Student deletion error:', err);
      return false;
    }
  }, [fetchStudents]);

  // Initial fetch and refetch when filters change
  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  return {
    students,
    loading,
    error,
    createStudent,
    updateStudent,
    deleteStudent,
    refetch: fetchStudents,
    setFilters,
    filters
  };
}
