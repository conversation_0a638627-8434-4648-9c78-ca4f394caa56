// src/app/staff-management/page.tsx
'use client';

import { useState } from 'react';

export default function StaffManagement() {
  const [activeView, setActiveView] = useState<'org' | 'list'>('org');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Floating Header */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-xl border-b border-slate-200/50 shadow-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl-app font-bold text-slate-900 tracking-tight">Staff Hub</h1>
                <p className="text-sm-app text-slate-600 font-medium">
                  Manage your educational team with ease
                </p>
              </div>
            </div>

            {/* View Toggle */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center bg-slate-100 rounded-lg p-1">
                <button
                  onClick={() => setActiveView('org')}
                  className={`px-3 py-1.5 rounded-md text-sm-app font-medium transition-all duration-200 ${
                    activeView === 'org'
                      ? 'bg-white text-indigo-600 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  <svg className="w-4 h-4 mr-1.5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Org Chart
                </button>
                <button
                  onClick={() => setActiveView('list')}
                  className={`px-3 py-1.5 rounded-md text-sm-app font-medium transition-all duration-200 ${
                    activeView === 'list'
                      ? 'bg-white text-indigo-600 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  <svg className="w-4 h-4 mr-1.5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                  List
                </button>
              </div>

              <button className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white text-sm-app font-medium py-2.5 px-4 rounded-xl flex items-center space-x-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add Staff</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="px-4 py-2 bg-white/60 backdrop-blur-sm border-b border-slate-200/50">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                placeholder="Search staff members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-slate-300/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm-app"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="text-sm-app border border-slate-300/50 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200"
            >
              <option value="all">All Departments</option>
              <option value="mathematics">Mathematics</option>
              <option value="science">Science</option>
              <option value="english">English</option>
              <option value="administration">Administration</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="text-sm-app border border-slate-300/50 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="on-leave">On Leave</option>
            </select>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="px-4 py-2">
        {activeView === 'org' && (
          <div className="bg-gradient-to-br from-slate-50 to-white/80 backdrop-blur-sm rounded-xl p-3 border border-white/50 shadow-lg">
            <div className="text-center mb-3">
              <h3 className="text-lg font-bold text-slate-900 mb-1">Organizational Structure</h3>
              <p className="text-slate-600 text-xs">Class-Level Staff Management (Nursery - 5th Grade)</p>
            </div>

            {/* Hierarchical Tree Structure */}
            <div className="relative">
              {/* Principal - Level 1 */}
              <div className="flex justify-center mb-2">
                <div className="relative">
                  <div className="bg-gradient-to-r from-indigo-600 to-purple-700 rounded-lg p-2.5 text-white text-center shadow-md transform hover:scale-105 transition-all duration-300">
                    <div className="w-6 h-6 bg-white/20 rounded-md flex items-center justify-center mx-auto mb-1 backdrop-blur-sm">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h4 className="text-xs font-bold">Dr. Robert Smith</h4>
                    <p className="text-indigo-100 text-xs">Principal</p>
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-indigo-600 rotate-45"></div>
                  </div>
                </div>
              </div>

              {/* Connecting Line */}
              <div className="flex justify-center mb-1">
                <div className="w-px h-2 bg-gradient-to-b from-indigo-300 to-teal-300"></div>
              </div>

              {/* Academic Coordinator - Level 2 */}
              <div className="flex justify-center mb-2">
                <div className="relative">
                  <div className="bg-gradient-to-r from-teal-500 to-emerald-600 rounded-md p-2 text-white text-center shadow-sm transform hover:scale-105 transition-all duration-300">
                    <div className="w-5 h-5 bg-white/20 rounded-sm flex items-center justify-center mx-auto mb-1 backdrop-blur-sm">
                      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <h4 className="text-xs font-bold">Ms. Sarah Williams</h4>
                    <p className="text-teal-100 text-xs">Academic Coordinator</p>
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-teal-500 rotate-45"></div>
                  </div>
                </div>
              </div>

              {/* Class Cards Section */}
              <div className="mt-3">
                <div className="text-center mb-2">
                  <div className="inline-flex items-center px-2 py-0.5 bg-slate-100 rounded-full">
                    <div className="w-1 h-1 bg-slate-400 rounded-full mr-1"></div>
                    <span className="text-slate-700 font-medium text-xs">CLASS TEACHERS & SUPPORT STAFF</span>
                  </div>
                </div>
                
                {/* Professional Class Cards Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
                  {/* Nursery Class */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-pink-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-pink-400 to-rose-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Nursery</h5>
                        </div>
                        <div className="w-6 h-6 bg-pink-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-pink-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Emma Wilson', role: 'Class Teacher', avatar: 'EW' },
                          { name: 'Ms. Lucy Brown', role: 'Assistant', avatar: 'LB' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-pink-50/50 rounded-md hover:bg-pink-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-pink-400 to-rose-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Prep Class */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-amber-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-orange-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-orange-400 to-amber-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Prep</h5>
                        </div>
                        <div className="w-6 h-6 bg-orange-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-orange-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Sophie Davis', role: 'Class Teacher', avatar: 'SD' },
                          { name: 'Ms. Grace Taylor', role: 'Assistant', avatar: 'GT' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-orange-50/50 rounded-md hover:bg-orange-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-orange-400 to-amber-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Class 1 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-blue-400 to-cyan-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Class 1</h5>
                        </div>
                        <div className="w-6 h-6 bg-blue-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-blue-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Maria Rodriguez', role: 'Class Teacher', avatar: 'MR' },
                          { name: 'Ms. Anna Johnson', role: 'Subject Teacher', avatar: 'AJ' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-blue-50/50 rounded-md hover:bg-blue-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Second Row */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
                  {/* Class 2 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-violet-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-purple-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-purple-400 to-violet-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Class 2</h5>
                        </div>
                        <div className="w-6 h-6 bg-purple-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-purple-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Jennifer Lee', role: 'Class Teacher', avatar: 'JL' },
                          { name: 'Mr. David Kim', role: 'Subject Teacher', avatar: 'DK' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-purple-50/50 rounded-md hover:bg-purple-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-purple-400 to-violet-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Class 3 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-green-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-green-400 to-emerald-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Class 3</h5>
                        </div>
                        <div className="w-6 h-6 bg-green-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-green-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Mr. Alex Thompson', role: 'Class Teacher', avatar: 'AT' },
                          { name: 'Ms. Rachel Green', role: 'Subject Teacher', avatar: 'RG' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-green-50/50 rounded-md hover:bg-green-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Class 4 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-teal-400 to-cyan-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-teal-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-teal-400 to-cyan-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Class 4</h5>
                        </div>
                        <div className="w-6 h-6 bg-teal-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-teal-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Lisa Wang', role: 'Class Teacher', avatar: 'LW' },
                          { name: 'Mr. Robert Chen', role: 'Subject Teacher', avatar: 'RC' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-teal-50/50 rounded-md hover:bg-teal-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-teal-400 to-cyan-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Third Row */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
                  {/* Class 5 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-indigo-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-indigo-400 to-blue-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Class 5</h5>
                        </div>
                        <div className="w-6 h-6 bg-indigo-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-indigo-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Mr. James Anderson', role: 'Class Teacher', avatar: 'JA' },
                          { name: 'Ms. Priya Patel', role: 'Subject Teacher', avatar: 'PP' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-indigo-50/50 rounded-md hover:bg-indigo-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-indigo-400 to-blue-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Support Staff */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-slate-400 to-gray-500 rounded-lg blur opacity-20 group-hover:opacity-30 transition-all duration-300"></div>
                    <div className="relative bg-white rounded-lg p-3 shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-1.5 h-4 bg-gradient-to-b from-slate-400 to-gray-500 rounded-full mr-2"></div>
                          <h5 className="text-sm font-bold text-slate-800">Support Staff</h5>
                        </div>
                        <div className="w-6 h-6 bg-slate-50 rounded-md flex items-center justify-center">
                          <div className="w-3 h-3 bg-slate-400 rounded-sm"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        {[
                          { name: 'Ms. Sarah Miller', role: 'Librarian', avatar: 'SM' },
                          { name: 'Mr. Tom Wilson', role: 'PE Teacher', avatar: 'TW' },
                        ].map((staff, index) => (
                          <div key={index} className="flex items-center p-1.5 bg-slate-50/50 rounded-md hover:bg-slate-50 transition-colors">
                            <div className="w-6 h-6 bg-gradient-to-br from-slate-400 to-gray-500 rounded-md flex items-center justify-center text-white font-bold text-xs mr-2">
                              {staff.avatar}
                            </div>
                            <div className="flex-1">
                              <h6 className="font-semibold text-slate-800 text-xs leading-tight">{staff.name}</h6>
                              <p className="text-slate-600 text-xs leading-tight">{staff.role}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Empty space for balance */}
                  <div className="hidden lg:block"></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'list' && (
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/50 shadow-lg overflow-hidden">
            <div className="p-6 border-b border-slate-200/50">
              <h3 className="text-xl-app font-bold text-slate-900">Staff Directory</h3>
              <p className="text-sm-app text-slate-600 mt-1">Complete list of all staff members</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-slate-50 to-slate-100">
                  <tr className="border-b border-slate-200">
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-4 px-6">Staff Member</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-4 px-6">Department</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-4 px-6">Role</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-4 px-6">Status</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-4 px-6">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {[
                    { name: 'Dr. Sarah Johnson', email: '<EMAIL>', department: 'Mathematics', role: 'Head of Department', status: 'active', avatar: 'SJ' },
                    { name: 'Prof. Michael Chen', email: '<EMAIL>', department: 'Science', role: 'Senior Teacher', status: 'active', avatar: 'MC' },
                    { name: 'Ms. Emily Davis', email: '<EMAIL>', department: 'English', role: 'Teacher', status: 'on-leave', avatar: 'ED' },
                    { name: 'Mr. James Wilson', email: '<EMAIL>', department: 'Administration', role: 'Admin Officer', status: 'active', avatar: 'JW' },
                    { name: 'Dr. Lisa Brown', email: '<EMAIL>', department: 'History', role: 'Teacher', status: 'active', avatar: 'LB' },
                    { name: 'Mr. David Kim', email: '<EMAIL>', department: 'Technology', role: 'IT Specialist', status: 'active', avatar: 'DK' },
                  ].map((staff, index) => (
                    <tr key={index} className="hover:bg-slate-50/50 transition-colors duration-150">
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-sm-app font-bold">
                            {staff.avatar}
                          </div>
                          <div>
                            <div className="text-sm-app text-slate-900 font-semibold">{staff.name}</div>
                            <div className="text-xs-app text-slate-500">{staff.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs-app font-medium bg-indigo-100 text-indigo-800">
                          {staff.department}
                        </span>
                      </td>
                      <td className="py-4 px-6 text-sm-app text-slate-600 font-medium">{staff.role}</td>
                      <td className="py-4 px-6">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs-app font-medium ${
                          staff.status === 'active' ? 'bg-green-100 text-green-800' :
                          staff.status === 'on-leave' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {staff.status === 'active' ? '● Active' : staff.status === 'on-leave' ? '● On Leave' : '● Inactive'}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <button className="p-2 text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 rounded-lg transition-all duration-150">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                          <button className="p-2 text-slate-600 hover:text-slate-700 hover:bg-slate-50 rounded-lg transition-all duration-150">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
