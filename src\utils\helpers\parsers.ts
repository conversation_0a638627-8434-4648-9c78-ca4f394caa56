// src/utils/helpers/parsers.ts
// Utility functions for parsing various data formats

/**
 * URL parsing utilities
 */
export const urlParsers = {
  /**
   * Parse query string to object
   */
  parseQueryString: (queryString: string): Record<string, string> => {
    const params = new URLSearchParams(queryString.startsWith('?') ? queryString.slice(1) : queryString);
    const result: Record<string, string> = {};
    
    params.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  },

  /**
   * Parse URL and extract components
   */
  parseUrl: (url: string): {
    protocol: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;
    params: Record<string, string>;
  } => {
    try {
      const urlObj = new URL(url);
      return {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        port: urlObj.port,
        pathname: urlObj.pathname,
        search: urlObj.search,
        hash: urlObj.hash,
        params: urlParsers.parseQueryString(urlObj.search)
      };
    } catch (error) {
      throw new Error(`Invalid URL: ${url}`);
    }
  },

  /**
   * Extract domain from URL
   */
  extractDomain: (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '';
    }
  }
};

/**
 * Date parsing utilities
 */
export const dateParsers = {
  /**
   * Parse date string in various formats
   */
  parseDate: (dateString: string): Date | null => {
    if (!dateString) return null;

    // Try ISO format first
    let date = new Date(dateString);
    if (!isNaN(date.getTime())) return date;

    // Try common formats
    const formats = [
      /^(\d{4})-(\d{2})-(\d{2})$/, // YYYY-MM-DD
      /^(\d{2})\/(\d{2})\/(\d{4})$/, // MM/DD/YYYY
      /^(\d{2})-(\d{2})-(\d{4})$/, // MM-DD-YYYY
      /^(\d{4})\/(\d{2})\/(\d{2})$/, // YYYY/MM/DD
    ];

    for (const format of formats) {
      const match = dateString.match(format);
      if (match) {
        const [, part1, part2, part3] = match;
        
        // Determine which parts are year, month, day based on format
        if (format.source.startsWith('^(\\d{4})')) {
          // Year first
          date = new Date(parseInt(part1), parseInt(part2) - 1, parseInt(part3));
        } else {
          // Month first (US format)
          date = new Date(parseInt(part3), parseInt(part1) - 1, parseInt(part2));
        }
        
        if (!isNaN(date.getTime())) return date;
      }
    }

    return null;
  },

  /**
   * Parse time string
   */
  parseTime: (timeString: string): { hours: number; minutes: number; seconds: number } | null => {
    const timeRegex = /^(\d{1,2}):(\d{2})(?::(\d{2}))?(?:\s*(AM|PM))?$/i;
    const match = timeString.match(timeRegex);
    
    if (!match) return null;

    let hours = parseInt(match[1]);
    const minutes = parseInt(match[2]);
    const seconds = parseInt(match[3] || '0');
    const ampm = match[4];

    if (ampm) {
      if (ampm.toUpperCase() === 'PM' && hours !== 12) {
        hours += 12;
      } else if (ampm.toUpperCase() === 'AM' && hours === 12) {
        hours = 0;
      }
    }

    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
      return null;
    }

    return { hours, minutes, seconds };
  },

  /**
   * Parse academic year string
   */
  parseAcademicYear: (yearString: string): { startYear: number; endYear: number } | null => {
    const patterns = [
      /^(\d{4})-(\d{4})$/, // 2023-2024
      /^(\d{4})-(\d{2})$/, // 2023-24
      /^(\d{4})$/, // 2023 (assumes next year)
    ];

    for (const pattern of patterns) {
      const match = yearString.match(pattern);
      if (match) {
        const startYear = parseInt(match[1]);
        let endYear: number;

        if (match[2]) {
          if (match[2].length === 2) {
            // Handle 2023-24 format
            endYear = parseInt(`${match[1].slice(0, 2)}${match[2]}`);
          } else {
            // Handle 2023-2024 format
            endYear = parseInt(match[2]);
          }
        } else {
          // Handle 2023 format
          endYear = startYear + 1;
        }

        if (endYear === startYear + 1) {
          return { startYear, endYear };
        }
      }
    }

    return null;
  }
};

/**
 * Number parsing utilities
 */
export const numberParsers = {
  /**
   * Parse number with locale support
   */
  parseNumber: (numberString: string, locale: string = 'en-US'): number | null => {
    if (!numberString) return null;

    // Remove common formatting characters
    const cleaned = numberString.replace(/[^\d.-]/g, '');
    const number = parseFloat(cleaned);

    return isNaN(number) ? null : number;
  },

  /**
   * Parse currency string
   */
  parseCurrency: (currencyString: string): { amount: number; currency: string } | null => {
    const currencyRegex = /^([A-Z]{3}|\$|₹|€|£)?\s*([0-9,]+\.?\d*)$/;
    const match = currencyString.trim().match(currencyRegex);

    if (!match) return null;

    const currencySymbol = match[1] || '';
    const amountString = match[2].replace(/,/g, '');
    const amount = parseFloat(amountString);

    if (isNaN(amount)) return null;

    // Map symbols to currency codes
    const currencyMap: Record<string, string> = {
      '$': 'USD',
      '₹': 'INR',
      '€': 'EUR',
      '£': 'GBP'
    };

    const currency = currencyMap[currencySymbol] || currencySymbol || 'USD';

    return { amount, currency };
  },

  /**
   * Parse percentage string
   */
  parsePercentage: (percentageString: string): number | null => {
    const cleaned = percentageString.replace('%', '').trim();
    const number = parseFloat(cleaned);
    
    return isNaN(number) ? null : number / 100;
  }
};

/**
 * Text parsing utilities
 */
export const textParsers = {
  /**
   * Parse name into components
   */
  parseName: (fullName: string): { firstName: string; lastName: string; middleName?: string } => {
    const parts = fullName.trim().split(/\s+/);
    
    if (parts.length === 1) {
      return { firstName: parts[0], lastName: '' };
    } else if (parts.length === 2) {
      return { firstName: parts[0], lastName: parts[1] };
    } else {
      return {
        firstName: parts[0],
        middleName: parts.slice(1, -1).join(' '),
        lastName: parts[parts.length - 1]
      };
    }
  },

  /**
   * Parse phone number
   */
  parsePhone: (phoneString: string): {
    countryCode?: string;
    areaCode?: string;
    number: string;
    formatted: string;
  } | null => {
    // Remove all non-digit characters
    const digits = phoneString.replace(/\D/g, '');
    
    if (digits.length < 10) return null;

    let countryCode: string | undefined;
    let areaCode: string | undefined;
    let number: string;

    if (digits.length === 10) {
      // US/Indian format without country code
      areaCode = digits.slice(0, 3);
      number = digits.slice(3);
    } else if (digits.length === 11 && digits.startsWith('1')) {
      // US format with country code
      countryCode = '1';
      areaCode = digits.slice(1, 4);
      number = digits.slice(4);
    } else if (digits.length === 12 && digits.startsWith('91')) {
      // Indian format with country code
      countryCode = '91';
      number = digits.slice(2);
    } else {
      // Other international formats
      number = digits;
    }

    const formatted = formatPhoneNumber(digits);

    return {
      countryCode,
      areaCode,
      number,
      formatted
    };
  },

  /**
   * Parse email address
   */
  parseEmail: (emailString: string): { username: string; domain: string; isValid: boolean } => {
    const emailRegex = /^([^@]+)@([^@]+)$/;
    const match = emailString.trim().match(emailRegex);

    if (!match) {
      return { username: '', domain: '', isValid: false };
    }

    return {
      username: match[1],
      domain: match[2],
      isValid: true
    };
  },

  /**
   * Parse address into components
   */
  parseAddress: (addressString: string): {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  } => {
    const parts = addressString.split(',').map(part => part.trim());
    
    // This is a simple parser - real-world address parsing is much more complex
    const result: any = {};
    
    if (parts.length >= 1) result.street = parts[0];
    if (parts.length >= 2) result.city = parts[1];
    if (parts.length >= 3) result.state = parts[2];
    if (parts.length >= 4) result.zipCode = parts[3];
    if (parts.length >= 5) result.country = parts[4];

    return result;
  }
};

/**
 * File parsing utilities
 */
export const fileParsers = {
  /**
   * Parse file name and extension
   */
  parseFileName: (fileName: string): { name: string; extension: string; baseName: string } => {
    const lastDotIndex = fileName.lastIndexOf('.');
    
    if (lastDotIndex === -1) {
      return { name: fileName, extension: '', baseName: fileName };
    }

    const baseName = fileName.substring(0, lastDotIndex);
    const extension = fileName.substring(lastDotIndex + 1);

    return { name: fileName, extension, baseName };
  },

  /**
   * Parse file size string
   */
  parseFileSize: (sizeString: string): number | null => {
    const sizeRegex = /^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)?$/i;
    const match = sizeString.trim().match(sizeRegex);

    if (!match) return null;

    const size = parseFloat(match[1]);
    const unit = (match[2] || 'B').toUpperCase();

    const multipliers: Record<string, number> = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
      'TB': 1024 * 1024 * 1024 * 1024
    };

    return size * (multipliers[unit] || 1);
  },

  /**
   * Parse MIME type
   */
  parseMimeType: (mimeType: string): { type: string; subtype: string; parameters: Record<string, string> } => {
    const parts = mimeType.split(';');
    const [type, subtype] = parts[0].split('/');
    
    const parameters: Record<string, string> = {};
    
    for (let i = 1; i < parts.length; i++) {
      const [key, value] = parts[i].split('=');
      if (key && value) {
        parameters[key.trim()] = value.trim();
      }
    }

    return { type, subtype, parameters };
  }
};

/**
 * Helper functions
 */
function formatPhoneNumber(digits: string): string {
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11) {
    return `+${digits.slice(0, 1)} (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  } else if (digits.length === 12) {
    return `+${digits.slice(0, 2)} ${digits.slice(2, 7)}-${digits.slice(7)}`;
  }
  
  return digits;
}
