// src/utils/supabase/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { decrypt } from './session';

// Define protected and public routes
const protectedRoutes = [
  '/dashboard',
  '/student-management',
  '/staff-management',
  '/academic-management',
  '/attendance-management',
  '/fee-management',
  '/reports',
  '/settings'
];

const publicRoutes = [
  '/',
  '/product',
  '/resources',
  '/auth'
];

export default async function middleware(req: NextRequest) {
  // Get the pathname from the request URL
  const path = req.nextUrl.pathname;

  console.log(`[MIDDLEWARE] Processing request for path: ${path}`);

  // Check if the current route is protected or public
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route));
  const isPublicRoute = publicRoutes.some(route => path === route || path.startsWith(route + '/'));

  // Get session from cookie with error handling
  let session = null;
  try {
    const cookie = req.cookies.get('session')?.value;
    session = await decrypt(cookie);
    console.log(`[MID<PERSON><PERSON>WARE] Session check for ${path}:`, !!session?.userId);
  } catch (error) {
    console.error(`[MIDDLEWARE] Session decrypt error for ${path}:`, error);
    // Continue with null session to handle gracefully
  }
  
  // Redirect to auth page if trying to access protected route without session
  if (isProtectedRoute && !session?.userId) {
    console.log(`[MIDDLEWARE] Redirecting ${path} to /auth - no valid session`);
    return NextResponse.redirect(new URL('/auth', req.nextUrl));
  }

  // Redirect to dashboard if authenticated user tries to access auth page
  if (path === '/auth' && session?.userId) {
    console.log(`[MIDDLEWARE] Redirecting /auth to /dashboard - user authenticated`);
    return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
  }
  
  // Check if session is expired with grace period
  if (session?.expiresAt) {
    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const gracePeriod = 5 * 60 * 1000; // 5 minutes grace period

    // Only redirect if session is expired beyond grace period
    if (currentTime > new Date(expirationTime.getTime() + gracePeriod)) {
      console.log(`[MIDDLEWARE] Session expired for ${path}, redirecting to /auth`);
      // Clear the expired session cookie
      const response = NextResponse.redirect(new URL('/auth', req.nextUrl));
      response.cookies.delete('session');
      return response;
    }
  }

  console.log(`[MIDDLEWARE] Request allowed for ${path}`);
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)' 
  ],
};
