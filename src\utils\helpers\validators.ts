// src/utils/helpers/validators.ts
// Pure validation utility functions

import { VALIDATION_PATTERNS, FIELD_LENGTHS, AGE_LIMITS, DATE_LIMITS } from '../constants/validation';

/**
 * Basic validation functions
 */
export const basicValidators = {
  /**
   * Check if value is not empty
   */
  isRequired: (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },

  /**
   * Check if value is a valid email
   */
  isEmail: (email: string): boolean => {
    return VALIDATION_PATTERNS.EMAIL.test(email.trim());
  },

  /**
   * Check if value is a valid phone number
   */
  isPhone: (phone: string): boolean => {
    const cleaned = phone.replace(/\D/g, '');
    return VALIDATION_PATTERNS.PHONE.test(cleaned);
  },

  /**
   * Check if value is a valid Indian phone number
   */
  isIndianPhone: (phone: string): boolean => {
    const cleaned = phone.replace(/\D/g, '');
    return VALIDATION_PATTERNS.PHONE_INDIAN.test(cleaned);
  },

  /**
   * Check if password meets requirements
   */
  isStrongPassword: (password: string): boolean => {
    return VALIDATION_PATTERNS.PASSWORD.test(password);
  },

  /**
   * Check if value is a valid name
   */
  isValidName: (name: string): boolean => {
    return VALIDATION_PATTERNS.NAME.test(name.trim());
  },

  /**
   * Check if value is a valid roll number
   */
  isValidRollNumber: (rollNumber: string): boolean => {
    return VALIDATION_PATTERNS.ROLL_NUMBER.test(rollNumber.trim());
  },

  /**
   * Check if value is a valid URL
   */
  isUrl: (url: string): boolean => {
    return VALIDATION_PATTERNS.URL.test(url.trim());
  },

  /**
   * Check if value is numeric
   */
  isNumeric: (value: string): boolean => {
    return VALIDATION_PATTERNS.NUMERIC.test(value);
  },

  /**
   * Check if value is alphanumeric
   */
  isAlphanumeric: (value: string): boolean => {
    return VALIDATION_PATTERNS.ALPHANUMERIC.test(value);
  }
};

/**
 * Length validation functions
 */
export const lengthValidators = {
  /**
   * Check minimum length
   */
  hasMinLength: (value: string, min: number): boolean => {
    return value.length >= min;
  },

  /**
   * Check maximum length
   */
  hasMaxLength: (value: string, max: number): boolean => {
    return value.length <= max;
  },

  /**
   * Check if length is within range
   */
  isLengthInRange: (value: string, min: number, max: number): boolean => {
    return value.length >= min && value.length <= max;
  },

  /**
   * Validate field length against predefined constraints
   */
  validateFieldLength: (value: string, field: keyof typeof FIELD_LENGTHS): boolean => {
    const limits = FIELD_LENGTHS[field];
    return lengthValidators.isLengthInRange(value, limits.min, limits.max);
  }
};

/**
 * Date validation functions
 */
export const dateValidators = {
  /**
   * Check if date is valid
   */
  isValidDate: (date: string): boolean => {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  },

  /**
   * Check if date is in the past
   */
  isPastDate: (date: string): boolean => {
    return new Date(date) < new Date();
  },

  /**
   * Check if date is in the future
   */
  isFutureDate: (date: string): boolean => {
    return new Date(date) > new Date();
  },

  /**
   * Check if date is today
   */
  isToday: (date: string): boolean => {
    const today = new Date();
    const checkDate = new Date(date);
    return today.toDateString() === checkDate.toDateString();
  },

  /**
   * Check if date is within range
   */
  isDateInRange: (date: string, minDate: string, maxDate: string): boolean => {
    const checkDate = new Date(date);
    return checkDate >= new Date(minDate) && checkDate <= new Date(maxDate);
  },

  /**
   * Check if birth date is valid for student
   */
  isValidStudentBirthDate: (birthDate: string): boolean => {
    if (!dateValidators.isValidDate(birthDate)) return false;
    
    const birth = new Date(birthDate);
    const currentYear = new Date().getFullYear();
    const birthYear = birth.getFullYear();
    
    // Check year range
    if (birthYear < DATE_LIMITS.MIN_BIRTH_YEAR || birthYear > currentYear) {
      return false;
    }
    
    // Check age range
    const age = currentYear - birthYear;
    return age >= AGE_LIMITS.STUDENT.min && age <= AGE_LIMITS.STUDENT.max;
  },

  /**
   * Check if academic year is valid
   */
  isValidAcademicYear: (year: number): boolean => {
    return year >= DATE_LIMITS.MIN_ACADEMIC_YEAR && year <= DATE_LIMITS.MAX_ACADEMIC_YEAR;
  }
};

/**
 * File validation functions
 */
export const fileValidators = {
  /**
   * Check if file size is within limit
   */
  isValidFileSize: (file: File, maxSizeInBytes: number): boolean => {
    return file.size <= maxSizeInBytes;
  },

  /**
   * Check if file type is allowed
   */
  isValidFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  /**
   * Check if file is an image
   */
  isImageFile: (file: File): boolean => {
    return file.type.startsWith('image/');
  },

  /**
   * Check if file is a document
   */
  isDocumentFile: (file: File): boolean => {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    return documentTypes.includes(file.type);
  },

  /**
   * Validate multiple files
   */
  validateFiles: (
    files: FileList | File[], 
    maxFiles: number, 
    maxSizePerFile: number, 
    allowedTypes: string[]
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const fileArray = Array.from(files);

    if (fileArray.length > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed`);
    }

    fileArray.forEach((file, index) => {
      if (!fileValidators.isValidFileSize(file, maxSizePerFile)) {
        errors.push(`File ${index + 1}: Size exceeds limit`);
      }
      
      if (!fileValidators.isValidFileType(file, allowedTypes)) {
        errors.push(`File ${index + 1}: Invalid file type`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

/**
 * Form validation functions
 */
export const formValidators = {
  /**
   * Validate student form data
   */
  validateStudentForm: (data: {
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    dateOfBirth: string;
    rollNumber: string;
    guardianName: string;
    guardianPhone: string;
    guardianEmail?: string;
  }): { isValid: boolean; errors: Record<string, string> } => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!basicValidators.isRequired(data.firstName)) {
      errors.firstName = 'First name is required';
    } else if (!basicValidators.isValidName(data.firstName)) {
      errors.firstName = 'Invalid first name format';
    } else if (!lengthValidators.validateFieldLength(data.firstName, 'FIRST_NAME')) {
      errors.firstName = 'First name length is invalid';
    }

    if (!basicValidators.isRequired(data.lastName)) {
      errors.lastName = 'Last name is required';
    } else if (!basicValidators.isValidName(data.lastName)) {
      errors.lastName = 'Invalid last name format';
    } else if (!lengthValidators.validateFieldLength(data.lastName, 'LAST_NAME')) {
      errors.lastName = 'Last name length is invalid';
    }

    if (!basicValidators.isRequired(data.dateOfBirth)) {
      errors.dateOfBirth = 'Date of birth is required';
    } else if (!dateValidators.isValidStudentBirthDate(data.dateOfBirth)) {
      errors.dateOfBirth = 'Invalid date of birth';
    }

    if (!basicValidators.isRequired(data.rollNumber)) {
      errors.rollNumber = 'Roll number is required';
    } else if (!basicValidators.isValidRollNumber(data.rollNumber)) {
      errors.rollNumber = 'Invalid roll number format';
    }

    if (!basicValidators.isRequired(data.guardianName)) {
      errors.guardianName = 'Guardian name is required';
    } else if (!basicValidators.isValidName(data.guardianName)) {
      errors.guardianName = 'Invalid guardian name format';
    }

    if (!basicValidators.isRequired(data.guardianPhone)) {
      errors.guardianPhone = 'Guardian phone is required';
    } else if (!basicValidators.isPhone(data.guardianPhone)) {
      errors.guardianPhone = 'Invalid guardian phone number';
    }

    // Optional fields
    if (data.email && !basicValidators.isEmail(data.email)) {
      errors.email = 'Invalid email format';
    }

    if (data.phone && !basicValidators.isPhone(data.phone)) {
      errors.phone = 'Invalid phone number';
    }

    if (data.guardianEmail && !basicValidators.isEmail(data.guardianEmail)) {
      errors.guardianEmail = 'Invalid guardian email format';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Validate authentication form data
   */
  validateAuthForm: (data: {
    email: string;
    password: string;
    confirmPassword?: string;
    name?: string;
  }): { isValid: boolean; errors: Record<string, string> } => {
    const errors: Record<string, string> = {};

    if (!basicValidators.isRequired(data.email)) {
      errors.email = 'Email is required';
    } else if (!basicValidators.isEmail(data.email)) {
      errors.email = 'Invalid email format';
    }

    if (!basicValidators.isRequired(data.password)) {
      errors.password = 'Password is required';
    } else if (!basicValidators.isStrongPassword(data.password)) {
      errors.password = 'Password must be at least 8 characters with uppercase, lowercase, and number';
    }

    if (data.confirmPassword !== undefined) {
      if (data.password !== data.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }

    if (data.name !== undefined) {
      if (!basicValidators.isRequired(data.name)) {
        errors.name = 'Name is required';
      } else if (!basicValidators.isValidName(data.name)) {
        errors.name = 'Invalid name format';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

/**
 * Composite validation function
 */
export function validateField(
  value: any,
  rules: Array<{
    validator: (value: any, ...args: any[]) => boolean;
    message: string;
    args?: any[];
  }>
): { isValid: boolean; message?: string } {
  for (const rule of rules) {
    const isValid = rule.args 
      ? rule.validator(value, ...rule.args)
      : rule.validator(value);
    
    if (!isValid) {
      return {
        isValid: false,
        message: rule.message
      };
    }
  }

  return { isValid: true };
}

/**
 * Debounced validation function
 */
export function createDebouncedValidator(
  validator: (value: any) => { isValid: boolean; message?: string },
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout;

  return (value: any, callback: (result: { isValid: boolean; message?: string }) => void) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      const result = validator(value);
      callback(result);
    }, delay);
  };
}
