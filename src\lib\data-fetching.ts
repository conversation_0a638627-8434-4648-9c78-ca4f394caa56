// src/lib/data-fetching.ts
'use client';

import { getSupabaseClient } from './supabase/client';
import type { SupabaseClient } from '@supabase/supabase-js';

export interface DataFetchingOptions {
  retries?: number;
  retryDelay?: number;
  timeout?: number;
  cache?: boolean;
  cacheKey?: string;
  cacheDuration?: number;
}

export interface DataFetchingResult<T> {
  data: T | null;
  error: string | null;
  loading: boolean;
  success: boolean;
}

// Simple in-memory cache
const cache = new Map<string, { data: any; timestamp: number; duration: number }>();

/**
 * Clear expired cache entries
 */
function clearExpiredCache() {
  const now = Date.now();
  for (const [key, entry] of cache.entries()) {
    if (now - entry.timestamp > entry.duration) {
      cache.delete(key);
    }
  }
}

/**
 * Get data from cache if valid
 */
function getCachedData<T>(key: string): T | null {
  clearExpiredCache();
  const entry = cache.get(key);
  if (entry && Date.now() - entry.timestamp < entry.duration) {
    return entry.data;
  }
  return null;
}

/**
 * Set data in cache
 */
function setCachedData<T>(key: string, data: T, duration: number) {
  cache.set(key, { data, timestamp: Date.now(), duration });
}

/**
 * Enhanced data fetching utility with retry logic, caching, and error handling
 */
export async function fetchWithRetry<T>(
  fetchFn: (client: SupabaseClient) => Promise<{ data: T | null; error: any }>,
  options: DataFetchingOptions = {}
): Promise<DataFetchingResult<T>> {
  const {
    retries = 3,
    retryDelay = 1000,
    timeout = 10000,
    cache: useCache = false,
    cacheKey,
    cacheDuration = 5 * 60 * 1000 // 5 minutes default
  } = options;

  // Check cache first
  if (useCache && cacheKey) {
    const cachedData = getCachedData<T>(cacheKey);
    if (cachedData) {
      return {
        data: cachedData,
        error: null,
        loading: false,
        success: true
      };
    }
  }

  let lastError: any = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeout);
      });

      // Create fetch promise
      const fetchPromise = (async () => {
        const client = await getSupabaseClient();
        return await fetchFn(client);
      })();

      // Race between fetch and timeout
      const result = await Promise.race([fetchPromise, timeoutPromise]);

      if (result.error) {
        throw new Error(result.error.message || 'Database query failed');
      }

      // Cache successful result
      if (useCache && cacheKey && result.data) {
        setCachedData(cacheKey, result.data, cacheDuration);
      }

      return {
        data: result.data,
        error: null,
        loading: false,
        success: true
      };
    } catch (error) {
      lastError = error;
      console.warn(`Fetch attempt ${attempt + 1} failed:`, error);

      // Don't retry on the last attempt
      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }
  }

  return {
    data: null,
    error: lastError instanceof Error ? lastError.message : 'Unknown error occurred',
    loading: false,
    success: false
  };
}

/**
 * Server-side data fetching for Next.js App Router
 */
export async function fetchServerData<T>(
  fetchFn: (client: SupabaseClient) => Promise<{ data: T | null; error: any }>,
  options: Omit<DataFetchingOptions, 'cache' | 'cacheKey' | 'cacheDuration'> = {}
): Promise<DataFetchingResult<T>> {
  const { retries = 1, timeout = 5000 } = options;

  try {
    // Import server-side client
    const { supabase } = await import('./supabase/client');
    
    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Server request timeout')), timeout);
    });

    // Create fetch promise
    const fetchPromise = fetchFn(supabase);

    // Race between fetch and timeout
    const result = await Promise.race([fetchPromise, timeoutPromise]);

    if (result.error) {
      throw new Error(result.error.message || 'Server database query failed');
    }

    return {
      data: result.data,
      error: null,
      loading: false,
      success: true
    };
  } catch (error) {
    console.error('Server fetch error:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Server error occurred',
      loading: false,
      success: false
    };
  }
}

/**
 * Batch data fetching utility
 */
export async function fetchBatch<T extends Record<string, any>>(
  fetchFunctions: {
    [K in keyof T]: (client: SupabaseClient) => Promise<{ data: T[K] | null; error: any }>
  },
  options: DataFetchingOptions = {}
): Promise<DataFetchingResult<T>> {
  try {
    const client = await getSupabaseClient();
    const keys = Object.keys(fetchFunctions) as (keyof T)[];
    
    // Execute all fetches in parallel
    const results = await Promise.allSettled(
      keys.map(key => fetchFunctions[key](client))
    );

    const data = {} as T;
    const errors: string[] = [];

    // Process results
    results.forEach((result, index) => {
      const key = keys[index];
      
      if (result.status === 'fulfilled') {
        if (result.value.error) {
          errors.push(`${String(key)}: ${result.value.error.message}`);
          data[key] = null as T[keyof T];
        } else {
          data[key] = result.value.data as T[keyof T];
        }
      } else {
        errors.push(`${String(key)}: ${result.reason.message}`);
        data[key] = null as T[keyof T];
      }
    });

    return {
      data,
      error: errors.length > 0 ? errors.join('; ') : null,
      loading: false,
      success: errors.length === 0
    };
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Batch fetch failed',
      loading: false,
      success: false
    };
  }
}

/**
 * Clear all cached data
 */
export function clearCache() {
  cache.clear();
}

/**
 * Clear specific cache entry
 */
export function clearCacheEntry(key: string) {
  cache.delete(key);
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  clearExpiredCache();
  return {
    size: cache.size,
    keys: Array.from(cache.keys())
  };
}
