'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '../components/auth/auth-provider';

export default function HomePage() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    // Set reasonable timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      // Force redirect to product page if auth takes too long
      router.push('/product');
    }, 8000); // Increased timeout

    // Don't do anything while still loading
    if (loading) {
      return () => clearTimeout(timeoutId);
    }

    // Clear timeout since we're about to redirect
    clearTimeout(timeoutId);

    // Once loading is complete, redirect based on auth state
    if (user && user.isAuthenticated) {
      router.push('/dashboard');
    } else {
      router.push('/product');
    }

    return () => clearTimeout(timeoutId);
  }, [router, user, loading]);

  // Show loading screen while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white text-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-slate-600 mb-2">Loading EduPro...</p>

          {/* Always provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-indigo-600 hover:text-indigo-700 underline text-sm"
            >
              Continue to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  // This should not be reached as useEffect will handle redirects
  return null;
}
