// src/app/attendance-management/page.tsx
'use client';

import { useState } from 'react';

export default function AttendanceManagementPage() {
  const [memberType, setMemberType] = useState('All Members');
  const [classFilter, setClassFilter] = useState('All Classes/Department');
  const [statusFilter, setStatusFilter] = useState('Filter by Status');
  const [currentPage, setCurrentPage] = useState(1);

  // Handler functions for quick actions
  const handleMarkAttendance = () => {
    // This would typically open a modal or navigate to an attendance marking form
    alert('Mark Attendance functionality would be implemented here');
  };

  const handleViewHistory = () => {
    // This would navigate to attendance history view
    alert('View Attendance History functionality would be implemented here');
  };

  const handleCourseReport = () => {
    // This would open course/section reports
    alert('Course/Section Report functionality would be implemented here');
  };

  const handleLateEarlyReport = () => {
    // This would open late/early reports
    alert('Late/Early Report functionality would be implemented here');
  };

  // Sample attendance data
  const attendanceData = [
    {
      id: 'STU001',
      name: '<PERSON>',
      role: 'Student',
      classDept: 'Class 10A',
      date: '2023-10-26',
      status: 'Present',
      attendance: '95%'
    },
    {
      id: 'STA001',
      name: 'Alice Brown',
      role: 'Staff',
      classDept: 'Science Dept.',
      date: '2023-10-26',
      status: 'Present',
      attendance: '98%'
    },
    {
      id: 'STU002',
      name: 'Jane Smith',
      role: 'Student',
      classDept: 'Class 9B',
      date: '2023-10-26',
      status: 'Absent',
      attendance: '78%'
    },
    {
      id: 'STU003',
      name: 'Mike Johnson',
      role: 'Student',
      classDept: 'Class 11C',
      date: '2023-10-26',
      status: 'Late',
      attendance: '88%'
    },
    {
      id: 'STA002',
      name: 'Robert Davis',
      role: 'Staff',
      classDept: 'Admin Office',
      date: '2023-10-26',
      status: 'On Leave',
      attendance: '92%'
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      'Present': 'bg-green-100 text-green-800 border border-green-200',
      'Absent': 'bg-red-100 text-red-800 border border-red-200',
      'Late': 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      'On Leave': 'bg-blue-100 text-blue-800 border border-blue-200'
    };

    return statusStyles[status as keyof typeof statusStyles] || 'bg-gray-100 text-gray-800 border border-gray-200';
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Mark Today's Attendance */}
          <div className="bg-white rounded-lg p-4 shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Mark Today's Attendance</h3>
            <p className="text-sm text-gray-600 mb-auto">Quickly mark attendance for students.</p>
            <button
              onClick={handleMarkAttendance}
              className="w-full bg-blue-200 text-blue-800 py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-300 transition-colors shadow-sm mt-3 flex items-center justify-center"
            >
              Mark Attendance →
            </button>
          </div>

          {/* View Attendance History */}
          <div className="bg-white rounded-lg p-4 shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">View Attendance History</h3>
            <p className="text-sm text-gray-600 mb-auto">Check past attendance records.</p>
            <button
              onClick={handleViewHistory}
              className="w-full bg-green-200 text-green-800 py-2 px-3 rounded-md text-sm font-medium hover:bg-green-300 transition-colors shadow-sm mt-3 flex items-center justify-center"
            >
              View History →
            </button>
          </div>

          {/* Course/Section Report */}
          <div className="bg-white rounded-lg p-4 shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Course/Section Report</h3>
            <p className="text-sm text-gray-600 mb-auto">View by course or section.</p>
            <button
              onClick={handleCourseReport}
              className="w-full bg-blue-200 text-blue-800 py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-300 transition-colors shadow-sm mt-3 flex items-center justify-center"
            >
              Generate Report →
            </button>
          </div>

          {/* Late/Early Report */}
          <div className="bg-white rounded-lg p-4 shadow-lg shadow-orange-500/20 hover:shadow-xl hover:shadow-orange-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Late/Early Report</h3>
            <p className="text-sm text-gray-600 mb-auto">Track punctuality & early leaves.</p>
            <button
              onClick={handleLateEarlyReport}
              className="w-full bg-orange-200 text-orange-800 py-2 px-3 rounded-md text-sm font-medium hover:bg-orange-300 transition-colors shadow-sm mt-3 flex items-center justify-center"
            >
              View Report →
            </button>
          </div>
        </div>
      </div>

      {/* Today's Attendance Overview */}
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Today's Attendance Overview</h2>
            <p className="text-sm text-gray-600 mt-1">Track and manage attendance for all members</p>
          </div>
          <div className="flex items-center space-x-3">
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
              <span>Mark Attendance</span>
            </button>
            <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Modern Search and Filters Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 relative overflow-hidden mb-4">
          <div className="p-4">
            {/* Compact Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <h3 className="text-base font-semibold text-gray-900">Search & Filter</h3>
                <span className="text-xs text-green-700 bg-green-50 px-2 py-1 rounded-full font-medium border border-green-100">
                  {attendanceData.length} records
                </span>
              </div>
              <button
                onClick={() => {
                  setMemberType('All Members');
                  setClassFilter('All Classes/Department');
                  setStatusFilter('Filter by Status');
                }}
                className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                title="Reset all filters"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Reset All</span>
              </button>
            </div>

            {/* Filter Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 items-end">
              {/* Member Type Filter */}
              <div className="group">
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Member Type
                </label>
                <div className="relative">
                  <select
                    value={memberType}
                    onChange={(e) => setMemberType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors appearance-none cursor-pointer"
                  >
                    <option>All Members</option>
                    <option>Students</option>
                    <option>Staff</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Class/Department Filter */}
              <div className="group">
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Class/Department
                </label>
                <div className="relative">
                  <select
                    value={classFilter}
                    onChange={(e) => setClassFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors appearance-none cursor-pointer"
                  >
                    <option>All Classes/Department</option>
                    <option>Class 9A</option>
                    <option>Class 10A</option>
                    <option>Science Dept.</option>
                    <option>Admin Office</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Date Filter */}
              <div className="group">
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  defaultValue="2023-10-26"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors"
                />
              </div>

              {/* Status Filter */}
              <div className="group">
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Status
                </label>
                <div className="relative">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors appearance-none cursor-pointer"
                  >
                    <option>Filter by Status</option>
                    <option>Present</option>
                    <option>Absent</option>
                    <option>Late</option>
                    <option>On Leave</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Apply Filter Button */}
              <div className="group">
                <label className="block text-xs font-medium text-gray-600 mb-1 opacity-0">
                  Action
                </label>
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  <span>Filter</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Attendance Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-800 text-white">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Member ID</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Name</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Role</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Class/Dept.</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Date</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Status</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Overall Attendance</th>
                  <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {attendanceData.map((record, index) => (
                  <tr key={record.id} className={index % 2 === 0 ? 'bg-white hover:bg-gray-50' : 'bg-gray-50/50 hover:bg-gray-100'}>
                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">{record.id}</td>
                    <td className="px-4 py-3 text-sm text-gray-900 font-semibold">{record.name}</td>
                    <td className="px-4 py-3 text-sm text-gray-600">{record.role}</td>
                    <td className="px-4 py-3 text-sm text-gray-600">{record.classDept}</td>
                    <td className="px-4 py-3 text-sm text-gray-600">{record.date}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(record.status)}`}>
                        {record.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">{record.attendance}</td>
                    <td className="px-4 py-3">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded hover:bg-blue-50">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button className="text-green-600 hover:text-green-800 transition-colors p-1 rounded hover:bg-green-50">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button className="text-red-600 hover:text-red-800 transition-colors p-1 rounded hover:bg-red-50">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50/50 border-t border-gray-200 rounded-b-lg">
            <div className="flex items-center text-sm text-gray-600">
              <span>Showing <span className="font-semibold text-gray-800">1</span> to <span className="font-semibold text-gray-800">5</span> of <span className="font-semibold text-gray-800">25</span> results</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                className="px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 hover:bg-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={currentPage === 1}
              >
                ← Previous
              </button>

              {[1, 2, 3, '...', 5].map((page, index) => (
                <button
                  key={index}
                  onClick={() => typeof page === 'number' && setCurrentPage(page)}
                  className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                    page === currentPage
                      ? 'bg-blue-600 text-white shadow-sm'
                      : page === '...'
                      ? 'text-gray-400 cursor-default'
                      : 'text-gray-700 hover:bg-white hover:text-gray-900'
                  }`}
                  disabled={page === '...'}
                >
                  {page}
                </button>
              ))}

              <button
                className="px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 hover:bg-white rounded-md transition-colors"
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
