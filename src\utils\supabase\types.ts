// src/utils/supabase/types.ts
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          role: 'student' | 'teacher' | 'admin' | 'parent';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          role?: 'student' | 'teacher' | 'admin' | 'parent';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          role?: 'student' | 'teacher' | 'admin' | 'parent';
          created_at?: string;
          updated_at?: string;
        };
      };
      classes: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      sections: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      academic_years: {
        Row: {
          id: string;
          year: string;
          start_date: string;
          end_date: string;
          is_active: boolean;
          is_current: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          year: string;
          start_date: string;
          end_date: string;
          is_active?: boolean;
          is_current?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          year?: string;
          start_date?: string;
          end_date?: string;
          is_active?: boolean;
          is_current?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      guardian_relations: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      students: {
        Row: {
          id: string;
          first_name: string;
          last_name: string;
          date_of_birth: string;
          gender: 'male' | 'female' | 'other';
          email: string | null;
          phone_number: string | null;
          address: string | null;
          guardian_name: string;
          guardian_relation_id: string;
          guardian_phone: string;
          guardian_email: string | null;
          guardian_address: string | null;
          emergency_contact: string | null;
          class_id: string;
          section_id: string;
          roll_number: string;
          previous_school: string | null;
          academic_year_id: string;
          profile_id: string | null;
          birth_certificate_url: string | null;
          previous_records_url: string | null;
          medical_records_url: string | null;
          photograph_url: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          first_name: string;
          last_name: string;
          date_of_birth: string;
          gender: 'male' | 'female' | 'other';
          email?: string | null;
          phone_number?: string | null;
          address?: string | null;
          guardian_name: string;
          guardian_relation_id: string;
          guardian_phone: string;
          guardian_email?: string | null;
          guardian_address?: string | null;
          emergency_contact?: string | null;
          class_id: string;
          section_id: string;
          roll_number: string;
          previous_school?: string | null;
          academic_year_id: string;
          profile_id?: string | null;
          birth_certificate_url?: string | null;
          previous_records_url?: string | null;
          medical_records_url?: string | null;
          photograph_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          first_name?: string;
          last_name?: string;
          date_of_birth?: string;
          gender?: 'male' | 'female' | 'other';
          email?: string | null;
          phone_number?: string | null;
          address?: string | null;
          guardian_name?: string;
          guardian_relation_id?: string;
          guardian_phone?: string;
          guardian_email?: string | null;
          guardian_address?: string | null;
          emergency_contact?: string | null;
          class_id?: string;
          section_id?: string;
          roll_number?: string;
          previous_school?: string | null;
          academic_year_id?: string;
          profile_id?: string | null;
          birth_certificate_url?: string | null;
          previous_records_url?: string | null;
          medical_records_url?: string | null;
          photograph_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

// Type exports for easier use
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Class = Database['public']['Tables']['classes']['Row'];
export type Section = Database['public']['Tables']['sections']['Row'];
export type AcademicYear = Database['public']['Tables']['academic_years']['Row'];
export type GuardianRelation = Database['public']['Tables']['guardian_relations']['Row'];
export type Student = Database['public']['Tables']['students']['Row'];

// Insert types
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ClassInsert = Database['public']['Tables']['classes']['Insert'];
export type SectionInsert = Database['public']['Tables']['sections']['Insert'];
export type AcademicYearInsert = Database['public']['Tables']['academic_years']['Insert'];
export type GuardianRelationInsert = Database['public']['Tables']['guardian_relations']['Insert'];
export type StudentInsert = Database['public']['Tables']['students']['Insert'];

// Update types
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];
export type ClassUpdate = Database['public']['Tables']['classes']['Update'];
export type SectionUpdate = Database['public']['Tables']['sections']['Update'];
export type AcademicYearUpdate = Database['public']['Tables']['academic_years']['Update'];
export type GuardianRelationUpdate = Database['public']['Tables']['guardian_relations']['Update'];
export type StudentUpdate = Database['public']['Tables']['students']['Update'];

// Additional utility types for the application
export interface StudentWithRelations extends Student {
  class?: Class;
  section?: Section;
  academic_year?: AcademicYear;
  guardian_relation?: GuardianRelation;
  profile?: Profile;
}

export interface EnrollmentData {
  student: StudentInsert;
  guardian: {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    relation_id: string;
  };
  emergency_contact?: string;
  documents?: {
    birth_certificate?: File;
    previous_records?: File;
    medical_records?: File;
    photograph?: File;
  };
}

