// src/utils/auth-state-manager.ts
'use client';

import React from 'react';
import { User } from './auth';

/**
 * Centralized authentication state manager to prevent race conditions
 * and ensure single source of truth for auth state
 */

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
  initialized: boolean;
}

interface AuthStateManager {
  getState(): AuthState;
  setState(updates: Partial<AuthState>): void;
  subscribe(callback: (state: AuthState) => void): () => void;
  isInitializing(): boolean;
  setInitializing(value: boolean): void;
}

class AuthStateManagerImpl implements AuthStateManager {
  private state: AuthState = {
    user: null,
    loading: true,
    error: null,
    initialized: false,
  };

  private subscribers: Set<(state: AuthState) => void> = new Set();
  private isInitializingFlag = false;

  getState(): AuthState {
    return { ...this.state };
  }

  setState(updates: Partial<AuthState>): void {
    const prevState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    // Only notify if state actually changed
    if (this.hasStateChanged(prevState, this.state)) {
      console.log('[AUTH_STATE] State updated:', {
        user: !!this.state.user,
        loading: this.state.loading,
        error: this.state.error,
        initialized: this.state.initialized
      });
      
      this.notifySubscribers();
    }
  }

  subscribe(callback: (state: AuthState) => void): () => void {
    this.subscribers.add(callback);
    
    // Immediately call with current state
    callback(this.getState());
    
    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);
    };
  }

  isInitializing(): boolean {
    return this.isInitializingFlag;
  }

  setInitializing(value: boolean): void {
    if (this.isInitializingFlag !== value) {
      console.log('[AUTH_STATE] Initialization state changed:', value);
      this.isInitializingFlag = value;
    }
  }

  private hasStateChanged(prev: AuthState, current: AuthState): boolean {
    return (
      prev.loading !== current.loading ||
      prev.error !== current.error ||
      prev.initialized !== current.initialized ||
      (prev.user?.email !== current.user?.email) ||
      (prev.user?.isAuthenticated !== current.user?.isAuthenticated)
    );
  }

  private notifySubscribers(): void {
    this.subscribers.forEach(callback => {
      try {
        callback(this.getState());
      } catch (error) {
        console.error('[AUTH_STATE] Subscriber callback error:', error);
      }
    });
  }

  // Reset state (useful for sign out)
  reset(): void {
    this.setState({
      user: null,
      loading: false,
      error: null,
      initialized: true,
    });
    this.setInitializing(false);
  }

  // Set authenticated user
  setUser(user: User): void {
    this.setState({
      user,
      loading: false,
      error: null,
      initialized: true,
    });
  }

  // Set error state
  setError(error: string): void {
    this.setState({
      error,
      loading: false,
      initialized: true,
    });
  }

  // Set loading state
  setLoading(loading: boolean): void {
    this.setState({ loading });
  }
}

// Singleton instance
const authStateManager = new AuthStateManagerImpl();

export default authStateManager;

/**
 * Hook to use auth state manager in React components
 */
export function useAuthState() {
  const [state, setState] = React.useState(authStateManager.getState());

  React.useEffect(() => {
    const unsubscribe = authStateManager.subscribe(setState);
    return unsubscribe;
  }, []);

  return {
    ...state,
    setUser: authStateManager.setUser.bind(authStateManager),
    setError: authStateManager.setError.bind(authStateManager),
    setLoading: authStateManager.setLoading.bind(authStateManager),
    reset: authStateManager.reset.bind(authStateManager),
  };
}

/**
 * Prevent multiple concurrent auth initializations
 */
export function withAuthInitializationLock<T extends (...args: any[]) => Promise<any>>(
  fn: T
): T {
  return (async (...args: Parameters<T>) => {
    if (authStateManager.isInitializing()) {
      console.log('[AUTH_STATE] Skipping initialization - already in progress');
      return;
    }

    try {
      authStateManager.setInitializing(true);
      return await fn(...args);
    } finally {
      authStateManager.setInitializing(false);
    }
  }) as T;
}



