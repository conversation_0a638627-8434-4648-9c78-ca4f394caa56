// src/utils/metadata.ts
import { Metadata } from 'next';

interface RouteMetadata {
  title: string;
  description: string;
  keywords?: string;
}

const routeMetadata: Record<string, RouteMetadata> = {
  // Dashboard routes
  'dashboard': {
    title: 'Dashboard - EduPro',
    description: 'EduPro management dashboard with comprehensive school administration tools',
    keywords: 'dashboard, school management, education administration'
  },
  'student-management': {
    title: 'Student Management - EduPro',
    description: 'Manage students, enrollment, and academic records',
    keywords: 'student management, enrollment, academic records'
  },
  'staff-management': {
    title: 'Staff Management - EduPro',
    description: 'Manage staff members, teachers, and administrative personnel',
    keywords: 'staff management, teachers, personnel'
  },
  'academic-management': {
    title: 'Academic Management - EduPro',
    description: 'Manage curriculum, subjects, classes, and academic planning',
    keywords: 'academic management, curriculum, subjects, classes'
  },
  'attendance-management': {
    title: 'Attendance Management - EduPro',
    description: 'Track and manage student and staff attendance records',
    keywords: 'attendance management, tracking, records'
  },
  'fee-management': {
    title: 'Fee Management - EduPro',
    description: 'Manage student fees, payments, and financial records',
    keywords: 'fee management, payments, financial records'
  },
  'reports': {
    title: 'Reports - EduPro',
    description: 'Generate and view comprehensive school reports and analytics',
    keywords: 'reports, analytics, school data'
  },
  'settings': {
    title: 'Settings - EduPro',
    description: 'EduPro account settings and profile management',
    keywords: 'settings, profile, account management'
  },
  // Public routes
  'product': {
    title: 'EduPro - Education Management System',
    description: 'Comprehensive education management system for modern educational institutions',
    keywords: 'school management, education, student management, enrollment'
  },
  'auth': {
    title: 'Sign In - EduPro',
    description: 'Sign in to your EduPro account to access the education management system',
    keywords: 'sign in, login, authentication'
  }
};

const defaultMetadata: RouteMetadata = {
  title: 'EduPro - Education Management System',
  description: 'Comprehensive education management system for modern educational institutions',
  keywords: 'school management, education, student management, enrollment'
};

/**
 * Generate metadata for a route based on the current path
 */
export function generateMetadata(route?: string | string[]): Metadata {
  let routeKey = '';
  
  if (Array.isArray(route)) {
    routeKey = route[0] || '';
  } else if (typeof route === 'string') {
    routeKey = route;
  }
  
  // Remove leading slash if present
  routeKey = routeKey.replace(/^\//, '');
  
  const metadata = routeMetadata[routeKey] || defaultMetadata;
  
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords,
    authors: [{ name: 'EduPro Team' }],
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
    },
  };
}

/**
 * Generate metadata for a specific route key
 */
export function getRouteMetadata(routeKey: string): Metadata {
  return generateMetadata(routeKey);
}

/**
 * Get page title for a route (for use in components)
 */
export function getPageTitle(route: string): string {
  const routeKey = route.replace(/^\//, '');
  const metadata = routeMetadata[routeKey] || defaultMetadata;
  return metadata.title.replace(' - EduPro', '');
}

/**
 * Get breadcrumb title for a route
 */
export function getBreadcrumbTitle(route: string): string {
  const routeTitles: Record<string, string> = {
    'dashboard': 'Dashboard',
    'student-management': 'Student Management',
    'staff-management': 'Staff Management',
    'academic-management': 'Academic Management',
    'attendance-management': 'Attendance Management',
    'fee-management': 'Fee Management',
    'reports': 'Reports',
    'settings': 'Settings',
  };

  const routeKey = route.replace(/^\//, '');
  return routeTitles[routeKey] || 'Dashboard';
}
